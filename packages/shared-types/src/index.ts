// 用户相关类型
export interface User {
  id: string
  username: string
  email: string
  phone?: string
  avatar?: string
  status: UserStatus
  roles: Role[]
  tenantId: string
  createdAt: Date
  updatedAt: Date
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

// 角色权限类型
export interface Role {
  id: string
  name: string
  code: string
  description?: string
  permissions: Permission[]
  tenantId: string
  isSystem: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Permission {
  id: string
  resource: string
  action: string
  conditions?: Record<string, any>
  description?: string
}

// 租户类型
export interface Tenant {
  id: string
  name: string
  code: string
  domain?: string
  logo?: string
  status: TenantStatus
  plan: TenantPlan
  settings: TenantSettings
  createdAt: Date
  updatedAt: Date
}

export enum TenantStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

export enum TenantPlan {
  FREE = 'free',
  BASIC = 'basic',
  PRO = 'pro',
  ENTERPRISE = 'enterprise'
}

export interface TenantSettings {
  maxUsers: number
  maxStorage: number
  features: string[]
  customization: Record<string, any>
}

// 插件类型
export interface Plugin {
  id: string
  name: string
  displayName: string
  version: string
  description: string
  type: PluginType
  category: string
  status: PluginStatus
  config: PluginConfig
  dependencies: string[]
  permissions: Permission[]
  routes: PluginRoute[]
  createdAt: Date
  updatedAt: Date
}

export enum PluginType {
  CORE = 'core',
  BUSINESS = 'business',
  AI = 'ai',
  INDUSTRY = 'industry'
}

export enum PluginStatus {
  INSTALLED = 'installed',
  ENABLED = 'enabled',
  DISABLED = 'disabled',
  ERROR = 'error'
}

export interface PluginConfig {
  autoLoad: boolean
  hotReload: boolean
  settings: Record<string, any>
}

export interface PluginRoute {
  path: string
  component: string
  meta: {
    title: string
    icon?: string
    permission?: string
  }
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  code?: string
  timestamp: number
}

export interface PaginatedResponse<T = any> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 通用查询类型
export interface QueryParams {
  page?: number
  pageSize?: number
  search?: string
  sort?: string
  order?: 'ASC' | 'DESC'
  filters?: Record<string, any>
}

// 文件上传类型
export interface FileInfo {
  id: string
  filename: string
  originalName: string
  mimetype: string
  size: number
  path: string
  url: string
  tenantId: string
  uploadedBy: string
  createdAt: Date
}

// 审计日志类型
export interface AuditLog {
  id: string
  action: string
  resource: string
  resourceId?: string
  userId: string
  tenantId: string
  ip: string
  userAgent: string
  details: Record<string, any>
  createdAt: Date
}

// 系统配置类型
export interface SystemConfig {
  key: string
  value: any
  type: ConfigType
  description?: string
  isPublic: boolean
  tenantId?: string
  updatedAt: Date
}

export enum ConfigType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  JSON = 'json'
}

// 通知类型
export interface Notification {
  id: string
  title: string
  content: string
  type: NotificationType
  userId: string
  tenantId: string
  isRead: boolean
  data?: Record<string, any>
  createdAt: Date
}

export enum NotificationType {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error'
}

// AI相关类型
export interface AIModel {
  id: string
  name: string
  provider: string
  type: AIModelType
  config: Record<string, any>
  isActive: boolean
  createdAt: Date
}

export enum AIModelType {
  LLM = 'llm',
  EMBEDDING = 'embedding',
  IMAGE = 'image',
  AUDIO = 'audio'
}

export interface AIChat {
  id: string
  title: string
  userId: string
  tenantId: string
  messages: AIChatMessage[]
  createdAt: Date
  updatedAt: Date
}

export interface AIChatMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  metadata?: Record<string, any>
  createdAt: Date
}
