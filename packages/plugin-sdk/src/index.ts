import { Permission, PluginRoute } from '@link-agent/shared-types'

// 插件接口
export interface Plugin {
  name: string
  version: string
  displayName?: string
  description?: string
  
  // 生命周期钩子
  onInstall?(context: PluginContext): Promise<void>
  onEnable?(context: PluginContext): Promise<void>
  onDisable?(context: PluginContext): Promise<void>
  onUninstall?(context: PluginContext): Promise<void>
  
  // 配置方法
  getPermissions?(): Permission[]
  getRoutes?(): PluginRoute[]
  getConfig?(): Record<string, any>
}

// 插件上下文
export interface PluginContext {
  // 数据库访问
  database: DatabaseContext
  
  // 事件总线
  eventBus: EventBusContext
  
  // 权限引擎
  permissions: PermissionContext
  
  // 路由管理
  router: RouterContext
  
  // 配置管理
  config: ConfigContext
  
  // 日志记录
  logger: LoggerContext
  
  // 缓存服务
  cache: CacheContext
  
  // 文件服务
  storage: StorageContext
}

// 数据库上下文
export interface DatabaseContext {
  // 获取仓库
  getRepository<T>(entity: new () => T): Repository<T>
  
  // 查询构建器
  createQueryBuilder(): QueryBuilder
  
  // 事务执行
  transaction<T>(fn: (manager: EntityManager) => Promise<T>): Promise<T>
  
  // 运行迁移
  runMigrations(): Promise<void>
  
  // 删除表
  dropTables(): Promise<void>
  
  // 跨插件数据查询
  queryPluginData(pluginId: string, query: any): Promise<any>
  
  // 数据共享
  shareData(targetPlugin: string, data: any): Promise<void>
}

// 事件总线上下文
export interface EventBusContext {
  // 发布事件
  emit(event: string, data: any): void
  
  // 订阅事件
  on(event: string, handler: EventHandler): void
  
  // 取消订阅
  off(event: string, handler: EventHandler): void
  
  // 一次性订阅
  once(event: string, handler: EventHandler): void
  
  // 发送消息给特定插件
  sendMessage(targetPlugin: string, message: any): void
  
  // 广播消息
  broadcast(message: any): void
}

// 权限上下文
export interface PermissionContext {
  // 注册权限
  registerPermissions(permissions: Permission[]): Promise<void>
  
  // 注销权限
  unregisterPermissions(): Promise<void>
  
  // 检查权限
  hasPermission(userId: string, resource: string, action: string): Promise<boolean>
  
  // 检查插件权限
  checkPluginPermission(pluginId: string, userId: string): Promise<boolean>
  
  // 授予权限
  grantPermission(userId: string, permission: Permission): Promise<void>
  
  // 撤销权限
  revokePermission(userId: string, permission: Permission): Promise<void>
}

// 路由上下文
export interface RouterContext {
  // 注册路由
  registerRoutes(routes: PluginRoute[]): void
  
  // 注销路由
  unregisterRoutes(): void
  
  // 获取路由
  getRoutes(): PluginRoute[]
}

// 配置上下文
export interface ConfigContext {
  // 获取配置
  get<T = any>(key: string, defaultValue?: T): T
  
  // 设置配置
  set(key: string, value: any): Promise<void>
  
  // 删除配置
  delete(key: string): Promise<void>
  
  // 获取所有配置
  getAll(): Record<string, any>
}

// 日志上下文
export interface LoggerContext {
  debug(message: string, meta?: any): void
  info(message: string, meta?: any): void
  warn(message: string, meta?: any): void
  error(message: string, meta?: any): void
}

// 缓存上下文
export interface CacheContext {
  // 获取缓存
  get<T = any>(key: string): Promise<T | null>
  
  // 设置缓存
  set(key: string, value: any, ttl?: number): Promise<void>
  
  // 删除缓存
  delete(key: string): Promise<void>
  
  // 清空缓存
  clear(): Promise<void>
  
  // 检查是否存在
  exists(key: string): Promise<boolean>
}

// 存储上下文
export interface StorageContext {
  // 上传文件
  upload(file: Buffer, filename: string, options?: UploadOptions): Promise<FileResult>
  
  // 下载文件
  download(path: string): Promise<Buffer>
  
  // 删除文件
  delete(path: string): Promise<void>
  
  // 获取文件URL
  getUrl(path: string): string
  
  // 检查文件是否存在
  exists(path: string): Promise<boolean>
}

// 类型定义
export type EventHandler = (data: any) => void | Promise<void>

export interface Repository<T> {
  find(options?: any): Promise<T[]>
  findOne(options?: any): Promise<T | null>
  save(entity: T): Promise<T>
  update(criteria: any, partialEntity: any): Promise<any>
  delete(criteria: any): Promise<any>
  count(options?: any): Promise<number>
}

export interface QueryBuilder {
  select(selection?: string | string[]): this
  from(entityClass: any, alias: string): this
  where(condition: string, parameters?: any): this
  andWhere(condition: string, parameters?: any): this
  orWhere(condition: string, parameters?: any): this
  orderBy(sort: string, order?: 'ASC' | 'DESC'): this
  limit(limit: number): this
  offset(offset: number): this
  getMany(): Promise<any[]>
  getOne(): Promise<any>
  getCount(): Promise<number>
}

export interface EntityManager {
  save<T>(entity: T): Promise<T>
  remove<T>(entity: T): Promise<T>
  find<T>(entityClass: new () => T, options?: any): Promise<T[]>
  findOne<T>(entityClass: new () => T, options?: any): Promise<T | null>
}

export interface UploadOptions {
  bucket?: string
  path?: string
  metadata?: Record<string, any>
}

export interface FileResult {
  filename: string
  path: string
  url: string
  size: number
  mimetype: string
}

// 插件装饰器
export function PluginDecorator(metadata: {
  name: string
  version: string
  displayName?: string
  description?: string
}) {
  return function <T extends new (...args: any[]) => {}>(constructor: T) {
    return class extends constructor {
      name = metadata.name
      version = metadata.version
      displayName = metadata.displayName
      description = metadata.description
    }
  }
}

// 事件装饰器
export function OnEvent(event: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    if (!target._eventHandlers) {
      target._eventHandlers = []
    }
    target._eventHandlers.push({
      event,
      handler: descriptor.value
    })
  }
}

// 权限装饰器
export function RequirePermission(resource: string, action: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function (...args: any[]) {
      const context = this.context as PluginContext
      const userId = args[0]?.userId // 假设第一个参数包含用户ID
      
      if (userId) {
        const hasPermission = await context.permissions.hasPermission(userId, resource, action)
        if (!hasPermission) {
          throw new Error(`权限不足: ${resource}:${action}`)
        }
      }
      
      return originalMethod.apply(this, args)
    }
  }
}

// 缓存装饰器
export function Cacheable(ttl: number = 3600) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function (...args: any[]) {
      const context = this.context as PluginContext
      const cacheKey = `${target.constructor.name}:${propertyKey}:${JSON.stringify(args)}`
      
      // 尝试从缓存获取
      const cached = await context.cache.get(cacheKey)
      if (cached !== null) {
        return cached
      }
      
      // 执行原方法
      const result = await originalMethod.apply(this, args)
      
      // 存入缓存
      await context.cache.set(cacheKey, result, ttl)
      
      return result
    }
  }
}
