-- 创建向量扩展
CREATE EXTENSION IF NOT EXISTS vector;

-- 创建UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建全文搜索扩展
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- 创建JSON操作扩展
CREATE EXTENSION IF NOT EXISTS btree_gin;

-- 创建开发数据库
CREATE DATABASE link_agent_dev;

-- 创建测试数据库
CREATE DATABASE link_agent_test;

-- 创建用户
CREATE USER link_agent_user WITH PASSWORD 'link_agent_password';

-- 授权
GRANT ALL PRIVILEGES ON DATABASE link_agent TO link_agent_user;
GRANT ALL PRIVILEGES ON DATABASE link_agent_dev TO link_agent_user;
GRANT ALL PRIVILEGES ON DATABASE link_agent_test TO link_agent_user;

-- 切换到主数据库
\c link_agent;

-- 创建向量扩展
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS btree_gin;

-- 授权给用户
GRANT ALL ON SCHEMA public TO link_agent_user;

-- 切换到开发数据库
\c link_agent_dev;

-- 创建向量扩展
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS btree_gin;

-- 授权给用户
GRANT ALL ON SCHEMA public TO link_agent_user;

-- 切换到测试数据库
\c link_agent_test;

-- 创建向量扩展
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS btree_gin;

-- 授权给用户
GRANT ALL ON SCHEMA public TO link_agent_user;
