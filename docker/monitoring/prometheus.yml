global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus 自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # API网关监控
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['host.docker.internal:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # AI服务监控
  - job_name: 'ai-service'
    static_configs:
      - targets: ['host.docker.internal:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # PostgreSQL 监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']

  # Redis 监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  # MinIO 监控
  - job_name: 'minio'
    static_configs:
      - targets: ['minio:9000']

  # Node Exporter (如果需要系统监控)
  - job_name: 'node'
    static_configs:
      - targets: ['host.docker.internal:9100']
