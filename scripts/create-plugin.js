#!/usr/bin/env node

/**
 * 插件创建脚本
 * 用于快速创建新的插件模板
 */

const fs = require('fs')
const path = require('path')
const readline = require('readline')

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

function question(query) {
  return new Promise(resolve => rl.question(query, resolve))
}

async function createPlugin() {
  console.log('🔌 Link Agent 插件创建工具\n')

  try {
    // 获取插件信息
    const pluginName = await question('插件名称 (例如: customer-management): ')
    const pluginDisplayName = await question('插件显示名称 (例如: 客户管理): ')
    const pluginDescription = await question('插件描述: ')
    const pluginCategory = await question('插件分类 (core/business/ai/industry): ')
    const pluginType = await question('插件类型 (例如: crm): ')

    if (!pluginName || !pluginDisplayName || !pluginCategory) {
      console.log('❌ 必填信息不能为空')
      process.exit(1)
    }

    // 创建插件目录
    const pluginDir = path.join('plugins', pluginCategory, pluginName)
    
    if (fs.existsSync(pluginDir)) {
      console.log('❌ 插件目录已存在')
      process.exit(1)
    }

    console.log(`\n📁 创建插件目录: ${pluginDir}`)
    fs.mkdirSync(pluginDir, { recursive: true })

    // 创建子目录
    const dirs = [
      'src/controllers',
      'src/services', 
      'src/entities',
      'src/dto',
      'src/migrations',
      'frontend/views',
      'frontend/components',
      'frontend/stores',
      'docs'
    ]

    dirs.forEach(dir => {
      fs.mkdirSync(path.join(pluginDir, dir), { recursive: true })
    })

    // 创建 package.json
    const packageJson = {
      name: `@link-agent/plugin-${pluginName}`,
      version: '1.0.0',
      description: pluginDescription,
      main: 'dist/index.js',
      scripts: {
        build: 'tsc',
        dev: 'tsc --watch',
        test: 'jest'
      },
      linkAgent: {
        type: pluginCategory,
        category: pluginType,
        displayName: pluginDisplayName,
        dependencies: [],
        permissions: [],
        routes: [],
        database: {
          entities: [],
          migrations: []
        }
      },
      dependencies: {
        '@nestjs/common': '^10.2.8',
        '@nestjs/core': '^10.2.8',
        '@link-agent/plugin-sdk': '^1.0.0'
      },
      devDependencies: {
        'typescript': '^5.3.0',
        '@types/node': '^20.10.0'
      }
    }

    fs.writeFileSync(
      path.join(pluginDir, 'package.json'),
      JSON.stringify(packageJson, null, 2)
    )

    // 创建插件入口文件
    const indexTs = `import { Plugin, PluginContext } from '@link-agent/plugin-sdk'

export default class ${toPascalCase(pluginName)}Plugin implements Plugin {
  name = '${pluginName}'
  version = '1.0.0'
  displayName = '${pluginDisplayName}'
  description = '${pluginDescription}'

  async onInstall(context: PluginContext): Promise<void> {
    console.log('安装插件:', this.displayName)
    // 插件安装逻辑
  }

  async onEnable(context: PluginContext): Promise<void> {
    console.log('启用插件:', this.displayName)
    // 插件启用逻辑
  }

  async onDisable(context: PluginContext): Promise<void> {
    console.log('禁用插件:', this.displayName)
    // 插件禁用逻辑
  }

  async onUninstall(context: PluginContext): Promise<void> {
    console.log('卸载插件:', this.displayName)
    // 插件卸载逻辑
  }
}
`

    fs.writeFileSync(path.join(pluginDir, 'src/index.ts'), indexTs)

    // 创建 TypeScript 配置
    const tsConfig = {
      compilerOptions: {
        target: 'ES2020',
        module: 'commonjs',
        lib: ['ES2020'],
        outDir: './dist',
        rootDir: './src',
        strict: true,
        esModuleInterop: true,
        skipLibCheck: true,
        forceConsistentCasingInFileNames: true,
        experimentalDecorators: true,
        emitDecoratorMetadata: true
      },
      include: ['src/**/*'],
      exclude: ['node_modules', 'dist']
    }

    fs.writeFileSync(
      path.join(pluginDir, 'tsconfig.json'),
      JSON.stringify(tsConfig, null, 2)
    )

    // 创建 README.md
    const readme = `# ${pluginDisplayName}

${pluginDescription}

## 功能特性

- 功能1
- 功能2
- 功能3

## 安装

\`\`\`bash
npm run plugin:install ${pluginName}
\`\`\`

## 配置

插件配置说明...

## API

### 接口列表

- \`GET /api/v1/${pluginName}\` - 获取列表
- \`POST /api/v1/${pluginName}\` - 创建
- \`PUT /api/v1/${pluginName}/:id\` - 更新
- \`DELETE /api/v1/${pluginName}/:id\` - 删除

## 开发

\`\`\`bash
cd plugins/${pluginCategory}/${pluginName}
npm install
npm run dev
\`\`\`

## 测试

\`\`\`bash
npm test
\`\`\`
`

    fs.writeFileSync(path.join(pluginDir, 'README.md'), readme)

    console.log('\n✅ 插件创建成功!')
    console.log(`📁 插件路径: ${pluginDir}`)
    console.log('\n📋 下一步:')
    console.log(`1. cd ${pluginDir}`)
    console.log('2. npm install')
    console.log('3. 开始开发你的插件')

  } catch (error) {
    console.error('❌ 创建插件失败:', error.message)
  } finally {
    rl.close()
  }
}

function toPascalCase(str) {
  return str
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('')
}

createPlugin()
