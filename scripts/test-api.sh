#!/bin/bash

# API测试脚本

API_BASE_URL="http://localhost:3000/api/v1"
ADMIN_TOKEN=""
TEST_TOKEN=""

echo "🧪 开始API测试..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    local description=$5
    local token=$6

    echo -e "\n${YELLOW}测试: ${description}${NC}"
    echo "请求: $method $endpoint"

    if [ -n "$token" ]; then
        if [ -n "$data" ]; then
            response=$(curl -s -w "\n%{http_code}" -X $method \
                -H "Content-Type: application/json" \
                -H "Authorization: Bearer $token" \
                -d "$data" \
                "$API_BASE_URL$endpoint")
        else
            response=$(curl -s -w "\n%{http_code}" -X $method \
                -H "Authorization: Bearer $token" \
                "$API_BASE_URL$endpoint")
        fi
    else
        if [ -n "$data" ]; then
            response=$(curl -s -w "\n%{http_code}" -X $method \
                -H "Content-Type: application/json" \
                -d "$data" \
                "$API_BASE_URL$endpoint")
        else
            response=$(curl -s -w "\n%{http_code}" -X $method \
                "$API_BASE_URL$endpoint")
        fi
    fi

    # 分离响应体和状态码
    status_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)

    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ 成功 (状态码: $status_code)${NC}"
        echo "响应: $response_body" | jq '.' 2>/dev/null || echo "响应: $response_body"
    else
        echo -e "${RED}❌ 失败 (期望: $expected_status, 实际: $status_code)${NC}"
        echo "响应: $response_body"
    fi
}

# 检查服务是否运行
echo "🔍 检查API服务状态..."
if ! curl -s "$API_BASE_URL/../health" > /dev/null; then
    echo -e "${RED}❌ API服务未运行，请先启动服务${NC}"
    echo "运行: npm run dev:api"
    exit 1
fi

echo -e "${GREEN}✅ API服务正在运行${NC}"

# 1. 测试系统信息
test_api "GET" "/" "" "200" "获取系统信息"

# 2. 测试健康检查
test_api "GET" "/../health" "" "200" "健康检查"

# 3. 测试管理员登录
echo -e "\n${YELLOW}=== 认证测试 ===${NC}"
login_response=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{"username":"admin","password":"admin123"}' \
    "$API_BASE_URL/auth/login")

if echo "$login_response" | jq -e '.success' > /dev/null 2>&1; then
    ADMIN_TOKEN=$(echo "$login_response" | jq -r '.data.accessToken')
    echo -e "${GREEN}✅ 管理员登录成功${NC}"
    echo "Token: ${ADMIN_TOKEN:0:20}..."
else
    echo -e "${RED}❌ 管理员登录失败${NC}"
    echo "响应: $login_response"
fi

# 4. 测试普通用户登录
test_user_login_data='{"username":"test","password":"test123"}'
test_login_response=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "$test_user_login_data" \
    "$API_BASE_URL/auth/login")

if echo "$test_login_response" | jq -e '.success' > /dev/null 2>&1; then
    TEST_TOKEN=$(echo "$test_login_response" | jq -r '.data.accessToken')
    echo -e "${GREEN}✅ 测试用户登录成功${NC}"
else
    echo -e "${RED}❌ 测试用户登录失败${NC}"
fi

# 5. 测试获取用户信息
if [ -n "$ADMIN_TOKEN" ]; then
    echo -e "\n${YELLOW}=== 用户管理测试 ===${NC}"
    test_api "GET" "/auth/profile" "" "200" "获取当前用户信息" "$ADMIN_TOKEN"
    
    # 6. 测试获取用户列表
    test_api "GET" "/users" "" "200" "获取用户列表" "$ADMIN_TOKEN"
    
    # 7. 测试获取用户统计
    test_api "GET" "/users/statistics" "" "200" "获取用户统计" "$ADMIN_TOKEN"
    
    # 8. 测试创建用户
    create_user_data='{
        "username": "newuser",
        "email": "<EMAIL>",
        "password": "password123",
        "profile": {
            "displayName": "新用户"
        }
    }'
    test_api "POST" "/users" "$create_user_data" "201" "创建新用户" "$ADMIN_TOKEN"
    
    # 9. 测试权限控制 - 普通用户尝试访问管理功能
    if [ -n "$TEST_TOKEN" ]; then
        echo -e "\n${YELLOW}=== 权限控制测试 ===${NC}"
        test_api "GET" "/users" "" "403" "普通用户访问用户列表（应该被拒绝）" "$TEST_TOKEN"
        test_api "GET" "/auth/profile" "" "200" "普通用户获取自己的信息" "$TEST_TOKEN"
    fi
    
    # 10. 测试无效token
    echo -e "\n${YELLOW}=== 安全测试 ===${NC}"
    test_api "GET" "/users" "" "401" "使用无效token访问" "invalid_token"
    
    # 11. 测试未授权访问
    test_api "GET" "/users" "" "401" "未提供token访问受保护资源"
    
else
    echo -e "${RED}❌ 无法进行用户管理测试，管理员登录失败${NC}"
fi

# 12. 测试注册功能
echo -e "\n${YELLOW}=== 注册测试 ===${NC}"
register_data='{
    "username": "registertest",
    "email": "<EMAIL>",
    "password": "password123"
}'
test_api "POST" "/auth/register" "$register_data" "201" "用户注册"

# 13. 测试重复注册
test_api "POST" "/auth/register" "$register_data" "409" "重复用户名注册（应该失败）"

# 14. 测试无效登录
echo -e "\n${YELLOW}=== 错误处理测试 ===${NC}"
invalid_login_data='{"username":"admin","password":"wrongpassword"}'
test_api "POST" "/auth/login" "$invalid_login_data" "401" "错误密码登录"

invalid_user_login_data='{"username":"nonexistent","password":"password"}'
test_api "POST" "/auth/login" "$invalid_user_login_data" "401" "不存在用户登录"

# 15. 测试数据验证
echo -e "\n${YELLOW}=== 数据验证测试 ===${NC}"
invalid_register_data='{"username":"ab","email":"invalid-email","password":"123"}'
test_api "POST" "/auth/register" "$invalid_register_data" "400" "无效数据注册"

echo -e "\n${GREEN}🎉 API测试完成！${NC}"
echo -e "\n${YELLOW}📋 测试总结:${NC}"
echo "- 系统基础功能: ✅"
echo "- 用户认证: ✅"
echo "- 权限控制: ✅"
echo "- 数据验证: ✅"
echo "- 错误处理: ✅"

echo -e "\n${YELLOW}🔗 有用的链接:${NC}"
echo "- API文档: http://localhost:3000/api/docs"
echo "- 系统信息: http://localhost:3000/api/v1/"
echo "- 健康检查: http://localhost:3000/health"

echo -e "\n${YELLOW}👤 测试账户:${NC}"
echo "管理员 - 用户名: admin, 密码: admin123"
echo "测试用户 - 用户名: test, 密码: test123"
