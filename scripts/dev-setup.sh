#!/bin/bash

# Link Agent 开发环境设置脚本

set -e

echo "🚀 开始设置 Link Agent 开发环境..."

# 检查必要的工具
check_requirements() {
    echo "📋 检查系统要求..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        echo "❌ Node.js 版本过低，需要 18+，当前版本: $(node -v)"
        exit 1
    fi
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python3 未安装，请先安装 Python 3.9+"
        exit 1
    fi
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    echo "✅ 系统要求检查通过"
}

# 复制环境变量文件
setup_env() {
    echo "📝 设置环境变量..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        echo "✅ 已创建 .env 文件，请根据需要修改配置"
    else
        echo "ℹ️  .env 文件已存在"
    fi
}

# 安装依赖
install_dependencies() {
    echo "📦 安装项目依赖..."
    
    # 安装根目录依赖
    npm install
    
    # 安装管理后台依赖
    if [ -d "apps/admin-web" ]; then
        echo "📦 安装管理后台依赖..."
        cd apps/admin-web
        npm install
        cd ../..
    fi
    
    # 安装会员门户依赖
    if [ -d "apps/member-web" ]; then
        echo "📦 安装会员门户依赖..."
        cd apps/member-web
        npm install
        cd ../..
    fi
    
    # 安装API网关依赖
    if [ -d "apps/api-gateway" ]; then
        echo "📦 安装API网关依赖..."
        cd apps/api-gateway
        npm install
        cd ../..
    fi
    
    # 安装AI服务依赖
    if [ -d "apps/ai-service" ]; then
        echo "📦 安装AI服务依赖..."
        cd apps/ai-service
        
        # 创建虚拟环境
        if [ ! -d "venv" ]; then
            python3 -m venv venv
        fi
        
        # 激活虚拟环境并安装依赖
        source venv/bin/activate
        pip install -r requirements.txt
        deactivate
        
        cd ../..
    fi
    
    echo "✅ 依赖安装完成"
}

# 启动基础服务
start_services() {
    echo "🐳 启动基础服务 (PostgreSQL, Redis, MinIO 等)..."
    
    # 启动 Docker 服务
    docker-compose -f docker/docker-compose.dev.yml up -d
    
    echo "⏳ 等待服务启动..."
    sleep 10
    
    # 检查服务状态
    echo "📊 检查服务状态..."
    docker-compose -f docker/docker-compose.dev.yml ps
    
    echo "✅ 基础服务启动完成"
}

# 初始化数据库
init_database() {
    echo "🗄️  初始化数据库..."
    
    # 等待 PostgreSQL 启动
    echo "⏳ 等待 PostgreSQL 启动..."
    sleep 5
    
    # 运行数据库迁移
    if [ -d "apps/api-gateway" ]; then
        cd apps/api-gateway
        npm run migration:run || echo "⚠️  数据库迁移失败，请检查配置"
        cd ../..
    fi
    
    echo "✅ 数据库初始化完成"
}

# 显示启动信息
show_info() {
    echo ""
    echo "🎉 开发环境设置完成！"
    echo ""
    echo "📋 服务信息:"
    echo "  🌐 管理后台: http://localhost:5173"
    echo "  🌐 会员门户: http://localhost:5174"
    echo "  🔌 API网关: http://localhost:3000"
    echo "  🤖 AI服务: http://localhost:8000"
    echo "  📚 API文档: http://localhost:3000/api/docs"
    echo ""
    echo "🗄️  数据库服务:"
    echo "  🐘 PostgreSQL: localhost:5432"
    echo "  🔴 Redis: localhost:6379"
    echo "  📦 MinIO: http://localhost:9001 (admin/admin)"
    echo "  🔍 Elasticsearch: http://localhost:9200"
    echo ""
    echo "📊 监控服务:"
    echo "  📈 Grafana: http://localhost:3000 (admin/admin)"
    echo "  🔥 Prometheus: http://localhost:9090"
    echo "  📋 Kibana: http://localhost:5601"
    echo ""
    echo "🚀 启动开发服务:"
    echo "  npm run dev          # 启动所有前端和后端服务"
    echo "  npm run dev:admin    # 仅启动管理后台"
    echo "  npm run dev:member   # 仅启动会员门户"
    echo "  npm run dev:api      # 仅启动API网关"
    echo ""
    echo "🛑 停止服务:"
    echo "  npm run docker:down  # 停止所有Docker服务"
    echo ""
}

# 主函数
main() {
    check_requirements
    setup_env
    install_dependencies
    start_services
    init_database
    show_info
}

# 执行主函数
main
