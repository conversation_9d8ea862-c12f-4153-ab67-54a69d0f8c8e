# 插件架构设计

## 设计目标

1. **模块化**: 每个插件独立开发、测试、部署
2. **热插拔**: 支持运行时动态加载/卸载插件
3. **隔离性**: 插件间数据和权限隔离
4. **扩展性**: 易于开发新插件和扩展现有功能
5. **标准化**: 统一的插件开发规范和API

## 插件分类

### 核心插件 (Core Plugins)
- **用户管理**: 用户注册、登录、资料管理
- **权限管理**: RBAC权限控制、角色管理
- **多租户**: 租户隔离、资源分配
- **审计日志**: 操作记录、安全审计

### 业务插件 (Business Plugins)
- **CRM**: 客户关系管理
- **销售管理**: 商机、订单、合同
- **项目管理**: 任务、进度、资源
- **工单管理**: 客服、售后、问题跟踪
- **会员管理**: 会员体系、积分、权益

### AI插件 (AI Plugins)
- **AI写作**: 文档生成、内容创作
- **AI分析**: 数据分析、报表生成
- **AI客服**: 智能问答、自动回复
- **智能代理**: 业务流程自动化

### 行业插件 (Industry Plugins)
- **制造业**: BOM管理、生产计划
- **服务业**: 服务流程、质量管理
- **零售业**: 库存管理、销售分析
- **教育行业**: 课程管理、学员跟踪

## 插件架构组件

### 1. 插件管理器 (Plugin Manager)

```typescript
interface PluginManager {
  // 插件生命周期管理
  install(plugin: Plugin): Promise<void>
  uninstall(pluginId: string): Promise<void>
  enable(pluginId: string): Promise<void>
  disable(pluginId: string): Promise<void>
  
  // 插件查询
  getPlugin(pluginId: string): Plugin | null
  getPlugins(): Plugin[]
  getEnabledPlugins(): Plugin[]
  
  // 插件依赖管理
  checkDependencies(plugin: Plugin): boolean
  resolveDependencies(plugin: Plugin): Plugin[]
}
```

### 2. 事件总线 (Event Bus)

```typescript
interface EventBus {
  // 事件发布订阅
  emit(event: string, data: any): void
  on(event: string, handler: Function): void
  off(event: string, handler: Function): void
  
  // 插件间通信
  sendMessage(targetPlugin: string, message: any): void
  broadcast(message: any): void
}
```

### 3. 权限引擎 (Permission Engine)

```typescript
interface PermissionEngine {
  // 权限检查
  hasPermission(userId: string, resource: string, action: string): boolean
  checkPluginPermission(pluginId: string, userId: string): boolean
  
  // 权限管理
  grantPermission(userId: string, permission: Permission): void
  revokePermission(userId: string, permission: Permission): void
}
```

### 4. 数据访问层 (Data Access Layer)

```typescript
interface DataAccessLayer {
  // 数据库操作
  getRepository<T>(entity: EntityClass<T>): Repository<T>
  createQueryBuilder(): QueryBuilder
  
  // 跨插件数据访问
  queryPluginData(pluginId: string, query: Query): Promise<any>
  shareData(targetPlugin: string, data: any): Promise<void>
}
```

## 插件开发规范

### 1. 插件结构

```
plugin-name/
├── package.json           # 插件元信息
├── plugin.config.js       # 插件配置
├── src/
│   ├── index.ts          # 插件入口
│   ├── controllers/      # 控制器
│   ├── services/         # 业务逻辑
│   ├── entities/         # 数据模型
│   ├── dto/              # 数据传输对象
│   └── migrations/       # 数据库迁移
├── frontend/
│   ├── views/            # 页面组件
│   ├── components/       # 通用组件
│   ├── stores/           # 状态管理
│   └── routes.ts         # 路由配置
└── docs/
    ├── README.md         # 插件说明
    ├── API.md            # API文档
    └── CHANGELOG.md      # 更新日志
```

### 2. 插件元信息 (package.json)

```json
{
  "name": "@link-agent/plugin-crm",
  "version": "1.0.0",
  "description": "客户关系管理插件",
  "main": "dist/index.js",
  "linkAgent": {
    "type": "business",
    "category": "crm",
    "dependencies": [
      "@link-agent/plugin-user-management@^1.0.0"
    ],
    "permissions": [
      "crm:customer:read",
      "crm:customer:write",
      "crm:customer:delete"
    ],
    "routes": [
      {
        "path": "/crm",
        "component": "CrmDashboard",
        "meta": {
          "title": "客户管理",
          "icon": "customer"
        }
      }
    ],
    "database": {
      "entities": ["Customer", "Contact", "Opportunity"],
      "migrations": ["./migrations/*.ts"]
    }
  }
}
```

### 3. 插件入口 (index.ts)

```typescript
import { Plugin, PluginContext } from '@link-agent/plugin-sdk'

export default class CrmPlugin implements Plugin {
  name = 'crm'
  version = '1.0.0'
  
  async onInstall(context: PluginContext): Promise<void> {
    // 插件安装逻辑
    await context.database.runMigrations()
    await context.permissions.registerPermissions(this.getPermissions())
  }
  
  async onEnable(context: PluginContext): Promise<void> {
    // 插件启用逻辑
    context.router.registerRoutes(this.getRoutes())
    context.eventBus.on('user:created', this.onUserCreated.bind(this))
  }
  
  async onDisable(context: PluginContext): Promise<void> {
    // 插件禁用逻辑
    context.eventBus.off('user:created', this.onUserCreated.bind(this))
  }
  
  async onUninstall(context: PluginContext): Promise<void> {
    // 插件卸载逻辑
    await context.database.dropTables()
    await context.permissions.unregisterPermissions()
  }
  
  private getPermissions(): Permission[] {
    return [
      { resource: 'crm:customer', actions: ['read', 'write', 'delete'] },
      { resource: 'crm:opportunity', actions: ['read', 'write'] }
    ]
  }
  
  private getRoutes(): Route[] {
    return [
      { path: '/crm/customers', component: 'CustomerList' },
      { path: '/crm/opportunities', component: 'OpportunityList' }
    ]
  }
  
  private onUserCreated(user: User): void {
    // 处理用户创建事件
  }
}
```

## 插件通信机制

### 1. 事件驱动通信

```typescript
// 发布事件
eventBus.emit('customer:created', { customerId: '123', name: 'ABC公司' })

// 订阅事件
eventBus.on('customer:created', (data) => {
  // 处理客户创建事件
  console.log('新客户创建:', data.name)
})
```

### 2. 直接消息通信

```typescript
// 发送消息给特定插件
eventBus.sendMessage('sales-plugin', {
  type: 'CREATE_OPPORTUNITY',
  data: { customerId: '123', amount: 100000 }
})

// 广播消息给所有插件
eventBus.broadcast({
  type: 'SYSTEM_MAINTENANCE',
  data: { startTime: '2024-01-01 02:00:00' }
})
```

### 3. 数据共享

```typescript
// 查询其他插件数据
const customers = await dataAccess.queryPluginData('crm-plugin', {
  entity: 'Customer',
  where: { status: 'active' }
})

// 共享数据给其他插件
await dataAccess.shareData('sales-plugin', {
  type: 'customer-list',
  data: customers
})
```

## 插件安全机制

### 1. 权限隔离
- 每个插件只能访问自己的数据
- 跨插件数据访问需要明确授权
- 基于角色的权限控制

### 2. 资源隔离
- 独立的数据库Schema
- 独立的文件存储空间
- 独立的缓存命名空间

### 3. 代码隔离
- 插件运行在独立的上下文中
- 防止插件间的代码冲突
- 沙箱环境保护

## 插件生态

### 1. 插件市场
- 官方插件商店
- 第三方插件发布
- 插件评级和评论
- 自动更新机制

### 2. 开发者工具
- 插件开发脚手架
- 调试和测试工具
- 文档生成工具
- 性能分析工具

### 3. 社区支持
- 开发者文档
- 示例代码库
- 技术支持论坛
- 开发者认证计划
