# 技术栈详细说明

## 前端技术栈

### 管理后台
- **Vue 3**: 渐进式框架，组合式API，更好的TypeScript支持
- **TypeScript**: 类型安全，提升开发效率和代码质量
- **Vite**: 快速构建工具，热重载，ES模块支持
- **Element Plus**: 企业级UI组件库，丰富的组件生态
- **Pinia**: 状态管理，Vue 3官方推荐
- **Vue Router**: 路由管理
- **Axios**: HTTP客户端
- **ECharts**: 数据可视化

### 会员门户
- **Vue 3 + TypeScript**: 与管理后台保持技术栈一致
- **Vant**: 移动端UI组件库，轻量级
- **PostCSS**: CSS处理工具
- **Tailwind CSS**: 原子化CSS框架

### 移动端
- **UniApp**: 跨平台开发，一套代码多端运行
- **Vue 3**: 与Web端保持一致的开发体验
- **uView**: UniApp生态UI组件库

## 后端技术栈

### 主服务 (NestJS)
- **NestJS**: 企业级Node.js框架，装饰器语法，模块化架构
- **TypeScript**: 全栈类型安全
- **TypeORM**: ORM框架，支持多数据库
- **Passport**: 认证中间件
- **JWT**: 无状态认证
- **Swagger**: API文档自动生成
- **Bull**: 队列处理
- **Winston**: 日志管理

### AI服务 (FastAPI)
- **FastAPI**: 高性能Python Web框架，自动API文档
- **Pydantic**: 数据验证和序列化
- **LangChain**: LLM应用开发框架
- **LangSmith**: LLM应用监控和调试
- **Celery**: 异步任务队列
- **SQLAlchemy**: Python ORM

### API网关
- **NestJS**: 统一网关服务
- **Rate Limiting**: 限流控制
- **Load Balancing**: 负载均衡
- **Circuit Breaker**: 熔断器
- **Request/Response Logging**: 请求响应日志

## 数据存储

### 主数据库
- **PostgreSQL 15+**: 
  - 强一致性，ACID事务
  - 丰富的数据类型支持
  - 扩展性强 (PGVector向量支持)
  - JSON/JSONB支持
  - 全文搜索

### 缓存
- **Redis 7+**:
  - 会话存储
  - 缓存热点数据
  - 分布式锁
  - 消息队列 (简单场景)
  - 限流计数器

### 文件存储
- **MinIO**:
  - S3兼容API
  - 分布式对象存储
  - 支持版本控制
  - 数据加密
  - 易于部署和维护

### 搜索引擎
- **Elasticsearch**:
  - 全文搜索
  - 日志分析
  - 实时数据分析
  - 多维度聚合查询

### 向量数据库
- **PGVector**:
  - PostgreSQL扩展
  - 简化部署和维护
  - 与主数据库集成
  - 支持相似度搜索

## AI技术栈

### LLM接入
- **国内模型**:
  - DeepSeek: 代码生成能力强
  - 通义千问: 阿里云生态
  - 文心一言: 百度生态
  - 智谱GLM: 清华技术

- **国外模型**:
  - OpenAI GPT-4: 综合能力最强
  - Claude: 长文本处理
  - Gemini: Google生态

### AI框架
- **LangChain**: 
  - LLM应用开发框架
  - 丰富的组件生态
  - 链式调用支持
  - 记忆和上下文管理

- **LangSmith**:
  - LLM应用监控
  - 调试和优化
  - 性能分析
  - A/B测试

### 向量化
- **文本向量化**:
  - BGE模型 (中文优化)
  - text-embedding-ada-002 (OpenAI)
  - m3e模型 (开源中文)

- **多模态向量化**:
  - CLIP (图文)
  - BLIP (图文)

## 基础设施

### 容器化
- **Docker**: 应用容器化
- **Docker Compose**: 本地开发环境
- **Multi-stage Build**: 优化镜像大小

### 编排
- **Kubernetes**: 生产环境容器编排
- **Helm**: K8s应用包管理
- **Ingress**: 流量入口管理
- **HPA**: 水平自动扩缩容

### CI/CD
- **GitHub Actions**: 持续集成/部署
- **Docker Registry**: 镜像仓库
- **ArgoCD**: GitOps部署
- **SonarQube**: 代码质量检查

### 监控
- **Prometheus**: 指标收集
- **Grafana**: 可视化监控
- **Jaeger**: 分布式链路追踪
- **AlertManager**: 告警管理

### 日志
- **ELK Stack**:
  - Elasticsearch: 日志存储和搜索
  - Logstash: 日志处理
  - Kibana: 日志可视化
- **Filebeat**: 日志收集

## 开发工具

### 代码质量
- **ESLint**: JavaScript/TypeScript代码检查
- **Prettier**: 代码格式化
- **Husky**: Git hooks
- **Commitizen**: 规范化提交信息

### 测试
- **Jest**: 单元测试
- **Cypress**: E2E测试
- **Supertest**: API测试
- **pytest**: Python测试框架

### 文档
- **VitePress**: 文档站点生成
- **Swagger/OpenAPI**: API文档
- **Storybook**: 组件文档

## 技术选型原则

1. **成熟稳定**: 选择经过生产验证的技术
2. **生态丰富**: 有活跃的社区和丰富的插件
3. **性能优秀**: 满足高并发和低延迟要求
4. **易于维护**: 代码可读性强，调试方便
5. **扩展性强**: 支持水平扩展和模块化
6. **团队熟悉**: 降低学习成本和开发风险
