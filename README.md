# AI业务一体化平台

## 项目概述

AI业务一体化平台是一个模块化、插件化的企业业务管理系统，通过AI技术增强，解决企业业务端的痛点，实现数据驱动决策。

## 技术架构

### 技术栈

#### 前端
- **管理后台**: Vue 3 + TypeScript + Vite + Element Plus + Pinia
- **会员门户**: Vue 3 + TypeScript + Vite + Vant + Pinia  
- **移动端**: UniApp (跨平台)

#### 后端
- **主服务**: NestJS + TypeScript + TypeORM
- **AI服务**: FastAPI + Python + LangChain
- **数据库**: PostgreSQL + Redis + MinIO
- **向量数据库**: PGVector (PostgreSQL扩展)

#### AI技术
- **LLM**: DeepSeek、通义千问、ChatGPT
- **框架**: LangChain + LangSmith
- **向量化**: BGE模型 或 OpenAI Embedding

### 项目结构

```
link-agent/
├── apps/                          # 应用目录
│   ├── admin-web/                 # 管理后台 (Vue3)
│   ├── member-web/                # 会员门户 (Vue3)
│   ├── mobile-app/                # 移动端 (UniApp)
│   ├── api-gateway/               # API网关 (NestJS)
│   ├── user-service/              # 用户服务 (NestJS)
│   ├── business-service/          # 业务服务 (NestJS)
│   ├── ai-service/                # AI服务 (FastAPI)
│   └── plugin-service/            # 插件服务 (NestJS)
├── packages/                      # 共享包
│   ├── shared-types/              # 共享类型定义
│   ├── shared-utils/              # 共享工具函数
│   ├── ui-components/             # 共享UI组件
│   └── plugin-sdk/                # 插件开发SDK
├── plugins/                       # 插件目录
│   ├── core/                      # 核心插件
│   │   ├── user-management/       # 用户管理
│   │   ├── permission-management/ # 权限管理
│   │   └── multi-tenant/          # 多租户
│   ├── business/                  # 业务插件
│   │   ├── crm/                   # 客户管理
│   │   ├── sales/                 # 销售管理
│   │   ├── project/               # 项目管理
│   │   ├── ticket/                # 工单管理
│   │   └── membership/            # 会员管理
│   ├── ai/                        # AI插件
│   │   ├── ai-writing/            # AI写作
│   │   ├── ai-analytics/          # AI分析
│   │   └── ai-agents/             # 智能代理
│   └── industry/                  # 行业插件
│       ├── manufacturing/         # 制造业
│       ├── service/               # 服务业
│       └── retail/                # 零售业
├── infrastructure/                # 基础设施
│   ├── docker/                    # Docker配置
│   ├── kubernetes/                # K8s配置
│   ├── database/                  # 数据库脚本
│   └── monitoring/                # 监控配置
├── docs/                          # 文档
│   ├── api/                       # API文档
│   ├── architecture/              # 架构文档
│   ├── plugins/                   # 插件开发文档
│   └── deployment/                # 部署文档
└── scripts/                       # 脚本
    ├── build/                     # 构建脚本
    ├── deploy/                    # 部署脚本
    └── dev/                       # 开发脚本
```

## 核心特性

### 1. 模块化插件架构
- 插件热插拔，支持动态加载/卸载
- 统一的插件开发SDK
- 插件间通信机制
- 插件权限隔离

### 2. 多租户支持
- 数据隔离
- 权限隔离
- 资源隔离
- 自定义配置

### 3. AI能力增强
- 智能写作助手
- 智能数据分析
- 智能客服
- 业务智能代理

### 4. 企业级特性
- RBAC权限控制
- 审计日志
- 数据备份恢复
- 高可用部署

## 开发阶段

### 阶段一 (0-6个月) - MVP
- [x] 项目架构设计
- [ ] 开发环境搭建
- [ ] 平台核心框架
- [ ] 会员体系模块
- [ ] AI服务集成
- [ ] 基础前端界面

### 阶段二 (6-18个月) - 扩展
- [ ] 核心业务模块 (CRM/销售/项目/工单)
- [ ] AI智能代理
- [ ] SaaS多租户
- [ ] 移动端应用

### 阶段三 (18-36个月) - 行业化
- [ ] 行业插件开发
- [ ] 第三方系统集成
- [ ] 插件生态建设
- [ ] 企业级部署

## 快速开始

### 环境要求
- Node.js 18+
- Python 3.9+
- PostgreSQL 15+
- Redis 7+
- Docker & Docker Compose

### 本地开发

```bash
# 克隆项目
git clone <repository-url>
cd link-agent

# 安装依赖
npm install

# 启动开发环境
npm run dev

# 启动所有服务 (Docker)
docker-compose up -d
```

## 贡献指南

请参考 [CONTRIBUTING.md](./CONTRIBUTING.md)

## 许可证

MIT License
