"""
AI服务主入口
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from contextlib import asynccontextmanager
import uvicorn
import logging

from app.core.config import settings
from app.core.logging import setup_logging
from app.api.v1.router import api_router
from app.core.exceptions import setup_exception_handlers


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    setup_logging()
    logging.info("🤖 AI服务启动中...")
    
    # 初始化AI模型
    from app.services.llm_service import LLMService
    llm_service = LLMService()
    await llm_service.initialize()
    
    # 初始化向量数据库
    from app.services.vector_service import VectorService
    vector_service = VectorService()
    await vector_service.initialize()
    
    logging.info("✅ AI服务启动完成")
    
    yield
    
    # 关闭时执行
    logging.info("🔄 AI服务关闭中...")
    await llm_service.cleanup()
    await vector_service.cleanup()
    logging.info("✅ AI服务已关闭")


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    app = FastAPI(
        title="Link Agent AI Service",
        description="AI业务一体化平台 - AI服务",
        version="1.0.0",
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        lifespan=lifespan
    )

    # 中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    app.add_middleware(GZipMiddleware, minimum_size=1000)

    # 异常处理
    setup_exception_handlers(app)

    # 路由
    app.include_router(api_router, prefix="/api/v1")

    @app.get("/")
    async def root():
        return {
            "message": "Link Agent AI Service",
            "version": "1.0.0",
            "status": "running"
        }

    @app.get("/health")
    async def health_check():
        return {"status": "healthy"}

    return app


app = create_app()

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
