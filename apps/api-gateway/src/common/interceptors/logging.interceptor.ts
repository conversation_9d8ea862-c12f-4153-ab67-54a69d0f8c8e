import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  <PERSON>H<PERSON><PERSON>,
  Logger
} from '@nestjs/common'
import { Observable } from 'rxjs'
import { tap } from 'rxjs/operators'

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name)

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest()
    const { method, url, body, query, params } = request
    const userAgent = request.get('User-Agent') || ''
    const ip = request.ip || request.connection.remoteAddress

    const now = Date.now()
    
    this.logger.log(
      `${method} ${url} - ${ip} - ${userAgent}`
    )

    return next.handle().pipe(
      tap(() => {
        const responseTime = Date.now() - now
        this.logger.log(
          `${method} ${url} - ${responseTime}ms`
        )
      })
    )
  }
}
