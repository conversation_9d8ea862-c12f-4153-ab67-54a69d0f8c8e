import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateInitialTables1703000000000 implements MigrationInterface {
  name = 'CreateInitialTables1703000000000'

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建租户表
    await queryRunner.query(`
      CREATE TABLE "tenants" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(100) NOT NULL,
        "code" character varying(50) NOT NULL,
        "domain" character varying(100),
        "logo" character varying(255),
        "status" character varying NOT NULL DEFAULT 'active',
        "plan" character varying NOT NULL DEFAULT 'free',
        "settings" jsonb,
        "metadata" jsonb,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        CONSTRAINT "PK_tenants" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_tenants_code" UNIQUE ("code")
      )
    `)

    // 创建角色表
    await queryRunner.query(`
      CREATE TABLE "roles" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(50) NOT NULL,
        "code" character varying(50) NOT NULL,
        "description" character varying(255),
        "tenant_id" uuid NOT NULL,
        "is_system" boolean NOT NULL DEFAULT false,
        "permissions" jsonb,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        CONSTRAINT "PK_roles" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_roles_code_tenant" UNIQUE ("code", "tenant_id")
      )
    `)

    // 创建用户表
    await queryRunner.query(`
      CREATE TABLE "users" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "username" character varying(50) NOT NULL,
        "email" character varying(100) NOT NULL,
        "phone" character varying(20),
        "avatar" character varying(255),
        "password" character varying(255) NOT NULL,
        "status" character varying NOT NULL DEFAULT 'active',
        "tenant_id" uuid NOT NULL,
        "last_login_at" TIMESTAMP,
        "last_login_ip" character varying(45),
        "profile" jsonb,
        "settings" jsonb,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        CONSTRAINT "PK_users" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_users_email_tenant" UNIQUE ("email", "tenant_id"),
        CONSTRAINT "UQ_users_username_tenant" UNIQUE ("username", "tenant_id")
      )
    `)

    // 创建用户角色关联表
    await queryRunner.query(`
      CREATE TABLE "user_roles" (
        "user_id" uuid NOT NULL,
        "role_id" uuid NOT NULL,
        CONSTRAINT "PK_user_roles" PRIMARY KEY ("user_id", "role_id")
      )
    `)

    // 添加外键约束
    await queryRunner.query(`
      ALTER TABLE "roles" 
      ADD CONSTRAINT "FK_roles_tenant" 
      FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE
    `)

    await queryRunner.query(`
      ALTER TABLE "users" 
      ADD CONSTRAINT "FK_users_tenant" 
      FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE
    `)

    await queryRunner.query(`
      ALTER TABLE "user_roles" 
      ADD CONSTRAINT "FK_user_roles_user" 
      FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE
    `)

    await queryRunner.query(`
      ALTER TABLE "user_roles" 
      ADD CONSTRAINT "FK_user_roles_role" 
      FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE CASCADE
    `)

    // 创建索引
    await queryRunner.query(`CREATE INDEX "IDX_users_email_tenant" ON "users" ("email", "tenant_id")`)
    await queryRunner.query(`CREATE INDEX "IDX_users_username_tenant" ON "users" ("username", "tenant_id")`)
    await queryRunner.query(`CREATE INDEX "IDX_roles_code_tenant" ON "roles" ("code", "tenant_id")`)
    await queryRunner.query(`CREATE INDEX "IDX_tenants_code" ON "tenants" ("code")`)
    await queryRunner.query(`CREATE INDEX "IDX_users_tenant_id" ON "users" ("tenant_id")`)
    await queryRunner.query(`CREATE INDEX "IDX_roles_tenant_id" ON "roles" ("tenant_id")`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除外键约束
    await queryRunner.query(`ALTER TABLE "user_roles" DROP CONSTRAINT "FK_user_roles_role"`)
    await queryRunner.query(`ALTER TABLE "user_roles" DROP CONSTRAINT "FK_user_roles_user"`)
    await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_users_tenant"`)
    await queryRunner.query(`ALTER TABLE "roles" DROP CONSTRAINT "FK_roles_tenant"`)

    // 删除表
    await queryRunner.query(`DROP TABLE "user_roles"`)
    await queryRunner.query(`DROP TABLE "users"`)
    await queryRunner.query(`DROP TABLE "roles"`)
    await queryRunner.query(`DROP TABLE "tenants"`)
  }
}
