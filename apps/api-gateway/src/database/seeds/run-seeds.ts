import { AppDataSource } from '../data-source'
import { Tenant, TenantStatus, TenantPlan } from '../../modules/user/entities/tenant.entity'
import { Role } from '../../modules/user/entities/role.entity'
import { User, UserStatus } from '../../modules/user/entities/user.entity'
import * as bcrypt from 'bcryptjs'

async function runSeeds() {
  try {
    // 初始化数据源
    await AppDataSource.initialize()
    console.log('数据库连接成功')

    const tenantRepository = AppDataSource.getRepository(Tenant)
    const roleRepository = AppDataSource.getRepository(Role)
    const userRepository = AppDataSource.getRepository(User)

    // 创建默认租户
    let defaultTenant = await tenantRepository.findOne({ where: { code: 'default' } })
    
    if (!defaultTenant) {
      defaultTenant = tenantRepository.create({
        name: '默认租户',
        code: 'default',
        status: TenantStatus.ACTIVE,
        plan: TenantPlan.ENTERPRISE,
        settings: {
          maxUsers: -1,
          maxStorage: -1,
          features: [
            'advanced_crm', 'advanced_reports', 'ai_writing', 'ai_analytics',
            'ai_agents', 'email_integration', 'sms_integration', 'api_access',
            'custom_plugins', 'white_label', 'sso'
          ],
          customization: {
            theme: 'default',
            colors: {
              primary: '#1890ff',
              secondary: '#722ed1'
            }
          }
        }
      })
      
      await tenantRepository.save(defaultTenant)
      console.log('✅ 默认租户创建成功')
    }

    // 创建默认角色
    const roles = [
      {
        name: '超级管理员',
        code: 'super_admin',
        description: '拥有所有权限的超级管理员',
        isSystem: true,
        permissions: [
          { resource: '*', action: '*', description: '所有权限' }
        ]
      },
      {
        name: '管理员',
        code: 'admin',
        description: '系统管理员',
        isSystem: true,
        permissions: [
          { resource: 'user', action: '*', description: '用户管理' },
          { resource: 'role', action: '*', description: '角色管理' },
          { resource: 'tenant', action: 'read', description: '租户查看' },
          { resource: 'tenant', action: 'update', description: '租户更新' }
        ]
      },
      {
        name: '普通用户',
        code: 'user',
        description: '普通用户',
        isSystem: true,
        permissions: [
          { resource: 'user', action: 'read', description: '查看用户信息' },
          { resource: 'user', action: 'update_self', description: '更新自己的信息' }
        ]
      }
    ]

    const createdRoles = []
    for (const roleData of roles) {
      let role = await roleRepository.findOne({
        where: { code: roleData.code, tenantId: defaultTenant.id }
      })

      if (!role) {
        role = roleRepository.create({
          ...roleData,
          tenantId: defaultTenant.id
        })
        await roleRepository.save(role)
        console.log(`✅ 角色 ${roleData.name} 创建成功`)
      }
      
      createdRoles.push(role)
    }

    // 创建超级管理员用户
    let adminUser = await userRepository.findOne({
      where: { username: 'admin', tenantId: defaultTenant.id }
    })

    if (!adminUser) {
      const hashedPassword = await bcrypt.hash('admin123', 10)
      
      adminUser = userRepository.create({
        username: 'admin',
        email: '<EMAIL>',
        password: hashedPassword,
        status: UserStatus.ACTIVE,
        tenantId: defaultTenant.id,
        profile: {
          displayName: '超级管理员'
        }
      })

      await userRepository.save(adminUser)

      // 分配超级管理员角色
      const superAdminRole = createdRoles.find(role => role.code === 'super_admin')
      if (superAdminRole) {
        adminUser.roles = [superAdminRole]
        await userRepository.save(adminUser)
      }

      console.log('✅ 超级管理员用户创建成功')
      console.log('   用户名: admin')
      console.log('   密码: admin123')
      console.log('   邮箱: <EMAIL>')
    }

    // 创建测试用户
    let testUser = await userRepository.findOne({
      where: { username: 'test', tenantId: defaultTenant.id }
    })

    if (!testUser) {
      const hashedPassword = await bcrypt.hash('test123', 10)
      
      testUser = userRepository.create({
        username: 'test',
        email: '<EMAIL>',
        password: hashedPassword,
        status: UserStatus.ACTIVE,
        tenantId: defaultTenant.id,
        profile: {
          displayName: '测试用户'
        }
      })

      await userRepository.save(testUser)

      // 分配普通用户角色
      const userRole = createdRoles.find(role => role.code === 'user')
      if (userRole) {
        testUser.roles = [userRole]
        await userRepository.save(testUser)
      }

      console.log('✅ 测试用户创建成功')
      console.log('   用户名: test')
      console.log('   密码: test123')
      console.log('   邮箱: <EMAIL>')
    }

    console.log('🎉 种子数据创建完成！')

  } catch (error) {
    console.error('❌ 种子数据创建失败:', error)
  } finally {
    await AppDataSource.destroy()
  }
}

runSeeds()
