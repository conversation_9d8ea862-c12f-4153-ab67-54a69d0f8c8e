import { Injectable } from '@nestjs/common'

@Injectable()
export class AppService {
  getSystemInfo(): {
    name: string
    version: string
    description: string
    status: string
    timestamp: number
  } {
    return {
      name: 'Link Agent API Gateway',
      version: '1.0.0',
      description: 'AI业务一体化平台 - API网关服务',
      status: 'running',
      timestamp: Date.now()
    }
  }

  healthCheck(): {
    status: string
    timestamp: number
    uptime: number
  } {
    return {
      status: 'healthy',
      timestamp: Date.now(),
      uptime: process.uptime()
    }
  }
}
