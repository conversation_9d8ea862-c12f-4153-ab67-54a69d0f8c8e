import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index
} from 'typeorm'
import { User } from './user.entity'
import { Role } from './role.entity'

export enum TenantStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

export enum TenantPlan {
  FREE = 'free',
  BASIC = 'basic',
  PRO = 'pro',
  ENTERPRISE = 'enterprise'
}

@Entity('tenants')
@Index(['code'], { unique: true })
export class Tenant {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ length: 100 })
  name: string

  @Column({ length: 50, unique: true })
  code: string

  @Column({ length: 100, nullable: true })
  domain?: string

  @Column({ length: 255, nullable: true })
  logo?: string

  @Column({
    type: 'enum',
    enum: TenantStatus,
    default: TenantStatus.ACTIVE
  })
  status: TenantStatus

  @Column({
    type: 'enum',
    enum: TenantPlan,
    default: TenantPlan.FREE
  })
  plan: TenantPlan

  @Column({ type: 'jsonb', nullable: true })
  settings?: TenantSettings

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date

  @Column({ name: 'deleted_at', nullable: true })
  deletedAt?: Date

  // 关联关系
  @OneToMany(() => User, user => user.tenant)
  users: User[]

  @OneToMany(() => Role, role => role.tenant)
  roles: Role[]

  // 虚拟属性
  get isActive(): boolean {
    return this.status === TenantStatus.ACTIVE && !this.deletedAt
  }

  // 方法
  canCreateUser(): boolean {
    if (!this.settings?.maxUsers) return true
    return (this.users?.length || 0) < this.settings.maxUsers
  }

  hasFeature(feature: string): boolean {
    return this.settings?.features?.includes(feature) || false
  }

  getStorageUsed(): number {
    // 这里应该计算实际使用的存储空间
    return 0
  }

  canUpload(fileSize: number): boolean {
    if (!this.settings?.maxStorage) return true
    const used = this.getStorageUsed()
    return (used + fileSize) <= this.settings.maxStorage
  }
}

export interface TenantSettings {
  maxUsers: number
  maxStorage: number // 字节
  features: string[]
  customization: {
    theme?: string
    logo?: string
    colors?: Record<string, string>
    [key: string]: any
  }
  integrations?: {
    email?: {
      provider: string
      config: Record<string, any>
    }
    sms?: {
      provider: string
      config: Record<string, any>
    }
    [key: string]: any
  }
}
