import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  JoinTable,
  ManyToOne,
  JoinColumn,
  OneToMany,
  Index
} from 'typeorm'
import { Exclude } from 'class-transformer'
import { Role } from './role.entity'
import { Tenant } from './tenant.entity'
import { MembershipSubscription } from '../../membership/entities/membership-subscription.entity'

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

@Entity('users')
@Index(['email', 'tenantId'], { unique: true })
@Index(['username', 'tenantId'], { unique: true })
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ length: 50 })
  username: string

  @Column({ length: 100 })
  email: string

  @Column({ length: 20, nullable: true })
  phone?: string

  @Column({ length: 255, nullable: true })
  avatar?: string

  @Column({ length: 255 })
  @Exclude()
  password: string

  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.ACTIVE
  })
  status: UserStatus

  @Column({ name: 'tenant_id' })
  tenantId: string

  @Column({ name: 'last_login_at', nullable: true })
  lastLoginAt?: Date

  @Column({ name: 'last_login_ip', length: 45, nullable: true })
  lastLoginIp?: string

  @Column({ type: 'jsonb', nullable: true })
  profile?: Record<string, any>

  @Column({ type: 'jsonb', nullable: true })
  settings?: Record<string, any>

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date

  @Column({ name: 'deleted_at', nullable: true })
  deletedAt?: Date

  // 关联关系
  @ManyToOne(() => Tenant, tenant => tenant.users)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant

  @ManyToMany(() => Role, role => role.users)
  @JoinTable({
    name: 'user_roles',
    joinColumn: { name: 'user_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' }
  })
  roles: Role[]

  @OneToMany(() => MembershipSubscription, subscription => subscription.user)
  subscriptions: MembershipSubscription[]

  // 虚拟属性
  get isActive(): boolean {
    return this.status === UserStatus.ACTIVE && !this.deletedAt
  }

  get displayName(): string {
    return this.profile?.displayName || this.username
  }

  // 方法
  hasRole(roleCode: string): boolean {
    return this.roles?.some(role => role.code === roleCode) || false
  }

  hasPermission(resource: string, action: string): boolean {
    if (!this.roles) return false
    
    return this.roles.some(role =>
      role.permissions?.some(permission =>
        permission.resource === resource && permission.action === action
      )
    )
  }

  toJSON() {
    const { password, ...result } = this
    return result
  }
}
