import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  ManyToOne,
  JoinColumn,
  Index
} from 'typeorm'
import { User } from './user.entity'
import { Tenant } from './tenant.entity'

@Entity('roles')
@Index(['code', 'tenantId'], { unique: true })
export class Role {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ length: 50 })
  name: string

  @Column({ length: 50 })
  code: string

  @Column({ length: 255, nullable: true })
  description?: string

  @Column({ name: 'tenant_id' })
  tenantId: string

  @Column({ name: 'is_system', default: false })
  isSystem: boolean

  @Column({ type: 'jsonb', nullable: true })
  permissions?: Permission[]

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date

  @Column({ name: 'deleted_at', nullable: true })
  deletedAt?: Date

  // 关联关系
  @ManyToOne(() => Tenant, tenant => tenant.roles)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant

  @ManyToMany(() => User, user => user.roles)
  users: User[]

  // 方法
  hasPermission(resource: string, action: string): boolean {
    return this.permissions?.some(permission =>
      permission.resource === resource && permission.action === action
    ) || false
  }

  addPermission(permission: Permission): void {
    if (!this.permissions) {
      this.permissions = []
    }
    
    const exists = this.permissions.some(p =>
      p.resource === permission.resource && p.action === permission.action
    )
    
    if (!exists) {
      this.permissions.push(permission)
    }
  }

  removePermission(resource: string, action: string): void {
    if (!this.permissions) return
    
    this.permissions = this.permissions.filter(p =>
      !(p.resource === resource && p.action === action)
    )
  }
}

export interface Permission {
  id?: string
  resource: string
  action: string
  conditions?: Record<string, any>
  description?: string
}
