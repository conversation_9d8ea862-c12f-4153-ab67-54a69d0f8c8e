import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  ParseUUIDPipe
} from '@nestjs/common'
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery
} from '@nestjs/swagger'
import { UserService } from '../services/user.service'
import {
  CreateUserDto,
  UpdateUserDto,
  QueryUserDto,
  ChangePasswordDto,
  UpdateUserStatusDto,
  UserResponseDto,
  UserListResponseDto,
  UserStatisticsDto
} from '../dto/user.dto'
import { JwtAuthGuard } from '../../../auth/guards/jwt-auth.guard'
import { PermissionGuard } from '../../../auth/guards/permission.guard'
import { RequirePermissions } from '../../../auth/decorators/permissions.decorator'
import { CurrentUser } from '../../../auth/decorators/current-user.decorator'
import { CurrentTenant } from '../../../auth/decorators/current-tenant.decorator'
import { User } from '../entities/user.entity'

@ApiTags('用户管理')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  @ApiOperation({ summary: '创建用户' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '用户创建成功',
    type: UserResponseDto
  })
  @UseGuards(PermissionGuard)
  @RequirePermissions('user', 'create')
  async create(
    @Body() createUserDto: CreateUserDto,
    @CurrentTenant() tenantId: string
  ): Promise<UserResponseDto> {
    const user = await this.userService.create(createUserDto, tenantId)
    return this.transformToResponseDto(user)
  }

  @Get()
  @ApiOperation({ summary: '获取用户列表' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取用户列表成功',
    type: UserListResponseDto
  })
  @UseGuards(PermissionGuard)
  @RequirePermissions('user', 'read')
  async findAll(
    @Query() query: QueryUserDto,
    @CurrentTenant() tenantId: string
  ): Promise<UserListResponseDto> {
    const { users, total } = await this.userService.findAll(query, tenantId)
    const totalPages = Math.ceil(total / query.pageSize)

    return {
      users: users.map(user => this.transformToResponseDto(user)),
      total,
      page: query.page,
      pageSize: query.pageSize,
      totalPages
    }
  }

  @Get('statistics')
  @ApiOperation({ summary: '获取用户统计信息' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取统计信息成功',
    type: UserStatisticsDto
  })
  @UseGuards(PermissionGuard)
  @RequirePermissions('user', 'read')
  async getStatistics(
    @CurrentTenant() tenantId: string
  ): Promise<UserStatisticsDto> {
    return this.userService.getStatistics(tenantId)
  }

  @Get('profile')
  @ApiOperation({ summary: '获取当前用户信息' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取用户信息成功',
    type: UserResponseDto
  })
  async getProfile(@CurrentUser() user: User): Promise<UserResponseDto> {
    const fullUser = await this.userService.findOne(user.id, user.tenantId)
    return this.transformToResponseDto(fullUser)
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取用户' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取用户成功',
    type: UserResponseDto
  })
  @UseGuards(PermissionGuard)
  @RequirePermissions('user', 'read')
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentTenant() tenantId: string
  ): Promise<UserResponseDto> {
    const user = await this.userService.findOne(id, tenantId)
    return this.transformToResponseDto(user)
  }

  @Patch('profile')
  @ApiOperation({ summary: '更新当前用户信息' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '更新用户信息成功',
    type: UserResponseDto
  })
  async updateProfile(
    @Body() updateUserDto: UpdateUserDto,
    @CurrentUser() user: User
  ): Promise<UserResponseDto> {
    // 普通用户只能更新自己的基本信息，不能更新角色等敏感信息
    const { roleIds, status, ...safeUpdateData } = updateUserDto
    const updatedUser = await this.userService.update(user.id, safeUpdateData, user.tenantId)
    return this.transformToResponseDto(updatedUser)
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新用户信息' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '更新用户成功',
    type: UserResponseDto
  })
  @UseGuards(PermissionGuard)
  @RequirePermissions('user', 'update')
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateUserDto: UpdateUserDto,
    @CurrentTenant() tenantId: string
  ): Promise<UserResponseDto> {
    const user = await this.userService.update(id, updateUserDto, tenantId)
    return this.transformToResponseDto(user)
  }

  @Patch(':id/status')
  @ApiOperation({ summary: '更新用户状态' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '更新用户状态成功',
    type: UserResponseDto
  })
  @UseGuards(PermissionGuard)
  @RequirePermissions('user', 'update')
  async updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateStatusDto: UpdateUserStatusDto,
    @CurrentTenant() tenantId: string
  ): Promise<UserResponseDto> {
    const user = await this.userService.updateStatus(id, updateStatusDto.status, tenantId)
    return this.transformToResponseDto(user)
  }

  @Post('change-password')
  @ApiOperation({ summary: '修改密码' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '密码修改成功'
  })
  async changePassword(
    @Body() changePasswordDto: ChangePasswordDto,
    @CurrentUser() user: User
  ): Promise<{ message: string }> {
    await this.userService.changePassword(
      user.id,
      changePasswordDto.oldPassword,
      changePasswordDto.newPassword,
      user.tenantId
    )
    return { message: '密码修改成功' }
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除用户' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '删除用户成功'
  })
  @UseGuards(PermissionGuard)
  @RequirePermissions('user', 'delete')
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentTenant() tenantId: string
  ): Promise<{ message: string }> {
    await this.userService.remove(id, tenantId)
    return { message: '用户删除成功' }
  }

  private transformToResponseDto(user: User): UserResponseDto {
    return {
      id: user.id,
      username: user.username,
      email: user.email,
      phone: user.phone,
      avatar: user.avatar,
      status: user.status,
      lastLoginAt: user.lastLoginAt,
      lastLoginIp: user.lastLoginIp,
      profile: user.profile,
      settings: user.settings,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      roles: user.roles,
      displayName: user.displayName,
      isActive: user.isActive
    }
  }
}
