import { Injectable, NotFoundException, ConflictException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { Tenant, TenantStatus, TenantPlan } from '../entities/tenant.entity'
import { Role } from '../entities/role.entity'

@Injectable()
export class TenantService {
  constructor(
    @InjectRepository(Tenant)
    private tenantRepository: Repository<Tenant>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>
  ) {}

  async create(createTenantDto: {
    name: string
    code: string
    domain?: string
    plan?: TenantPlan
  }): Promise<Tenant> {
    // 检查租户代码是否已存在
    const existingTenant = await this.tenantRepository.findOne({
      where: { code: createTenantDto.code }
    })

    if (existingTenant) {
      throw new ConflictException('租户代码已存在')
    }

    // 创建租户
    const tenant = this.tenantRepository.create({
      ...createTenantDto,
      status: TenantStatus.ACTIVE,
      plan: createTenantDto.plan || TenantPlan.FREE,
      settings: this.getDefaultSettings(createTenantDto.plan || TenantPlan.FREE)
    })

    const savedTenant = await this.tenantRepository.save(tenant)

    // 创建默认角色
    await this.createDefaultRoles(savedTenant.id)

    return savedTenant
  }

  async findByCode(code: string): Promise<Tenant | null> {
    return this.tenantRepository.findOne({
      where: { code, deletedAt: null }
    })
  }

  async findById(id: string): Promise<Tenant | null> {
    return this.tenantRepository.findOne({
      where: { id, deletedAt: null },
      relations: ['users', 'roles']
    })
  }

  async createDefaultTenant(): Promise<Tenant> {
    return this.create({
      name: '默认租户',
      code: 'default',
      plan: TenantPlan.FREE
    })
  }

  async updateSettings(id: string, settings: Partial<any>): Promise<Tenant> {
    const tenant = await this.findById(id)
    
    if (!tenant) {
      throw new NotFoundException('租户不存在')
    }

    tenant.settings = { ...tenant.settings, ...settings }
    return this.tenantRepository.save(tenant)
  }

  async updatePlan(id: string, plan: TenantPlan): Promise<Tenant> {
    const tenant = await this.findById(id)
    
    if (!tenant) {
      throw new NotFoundException('租户不存在')
    }

    tenant.plan = plan
    tenant.settings = this.getDefaultSettings(plan)
    
    return this.tenantRepository.save(tenant)
  }

  private getDefaultSettings(plan: TenantPlan) {
    const settingsMap = {
      [TenantPlan.FREE]: {
        maxUsers: 5,
        maxStorage: 1024 * 1024 * 100, // 100MB
        features: ['basic_crm', 'basic_reports'],
        customization: {
          theme: 'default',
          colors: {
            primary: '#1890ff',
            secondary: '#722ed1'
          }
        }
      },
      [TenantPlan.BASIC]: {
        maxUsers: 20,
        maxStorage: 1024 * 1024 * 1024, // 1GB
        features: ['basic_crm', 'basic_reports', 'ai_writing', 'email_integration'],
        customization: {
          theme: 'default',
          colors: {
            primary: '#1890ff',
            secondary: '#722ed1'
          }
        }
      },
      [TenantPlan.PRO]: {
        maxUsers: 100,
        maxStorage: 1024 * 1024 * 1024 * 10, // 10GB
        features: [
          'advanced_crm', 'advanced_reports', 'ai_writing', 'ai_analytics',
          'email_integration', 'sms_integration', 'api_access'
        ],
        customization: {
          theme: 'custom',
          colors: {
            primary: '#1890ff',
            secondary: '#722ed1'
          }
        }
      },
      [TenantPlan.ENTERPRISE]: {
        maxUsers: -1, // 无限制
        maxStorage: -1, // 无限制
        features: [
          'advanced_crm', 'advanced_reports', 'ai_writing', 'ai_analytics',
          'ai_agents', 'email_integration', 'sms_integration', 'api_access',
          'custom_plugins', 'white_label', 'sso'
        ],
        customization: {
          theme: 'custom',
          colors: {
            primary: '#1890ff',
            secondary: '#722ed1'
          }
        },
        integrations: {
          email: {
            provider: 'custom',
            config: {}
          },
          sms: {
            provider: 'custom',
            config: {}
          }
        }
      }
    }

    return settingsMap[plan]
  }

  private async createDefaultRoles(tenantId: string): Promise<void> {
    const defaultRoles = [
      {
        name: '超级管理员',
        code: 'super_admin',
        description: '拥有所有权限的超级管理员',
        isSystem: true,
        permissions: [
          { resource: '*', action: '*', description: '所有权限' }
        ]
      },
      {
        name: '管理员',
        code: 'admin',
        description: '系统管理员',
        isSystem: true,
        permissions: [
          { resource: 'user', action: '*', description: '用户管理' },
          { resource: 'role', action: '*', description: '角色管理' },
          { resource: 'tenant', action: 'read', description: '租户查看' },
          { resource: 'tenant', action: 'update', description: '租户更新' }
        ]
      },
      {
        name: '普通用户',
        code: 'user',
        description: '普通用户',
        isSystem: true,
        permissions: [
          { resource: 'user', action: 'read', description: '查看用户信息' },
          { resource: 'user', action: 'update_self', description: '更新自己的信息' }
        ]
      }
    ]

    for (const roleData of defaultRoles) {
      const role = this.roleRepository.create({
        ...roleData,
        tenantId
      })
      await this.roleRepository.save(role)
    }
  }
}
