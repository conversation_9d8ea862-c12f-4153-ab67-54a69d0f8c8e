import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository, FindManyOptions, Like } from 'typeorm'
import * as bcrypt from 'bcryptjs'
import { User, UserStatus } from '../entities/user.entity'
import { Role } from '../entities/role.entity'
import { CreateUserDto, UpdateUserDto, QueryUserDto } from '../dto/user.dto'

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>
  ) {}

  async create(createUserDto: CreateUserDto, tenantId: string): Promise<User> {
    // 检查用户名和邮箱是否已存在
    const existingUser = await this.userRepository.findOne({
      where: [
        { username: createUserDto.username, tenantId },
        { email: createUserDto.email, tenantId }
      ]
    })

    if (existingUser) {
      throw new ConflictException('用户名或邮箱已存在')
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(createUserDto.password, 10)

    // 创建用户
    const user = this.userRepository.create({
      ...createUserDto,
      password: hashedPassword,
      tenantId,
      status: UserStatus.ACTIVE
    })

    // 分配角色
    if (createUserDto.roleIds && createUserDto.roleIds.length > 0) {
      const roles = await this.roleRepository.findByIds(createUserDto.roleIds)
      user.roles = roles
    }

    return this.userRepository.save(user)
  }

  async findAll(query: QueryUserDto, tenantId: string): Promise<{ users: User[]; total: number }> {
    const { page = 1, pageSize = 10, search, status, roleId } = query
    
    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.roles', 'roles')
      .where('user.tenantId = :tenantId', { tenantId })
      .andWhere('user.deletedAt IS NULL')

    // 搜索条件
    if (search) {
      queryBuilder.andWhere(
        '(user.username ILIKE :search OR user.email ILIKE :search OR user.phone ILIKE :search)',
        { search: `%${search}%` }
      )
    }

    // 状态筛选
    if (status) {
      queryBuilder.andWhere('user.status = :status', { status })
    }

    // 角色筛选
    if (roleId) {
      queryBuilder.andWhere('roles.id = :roleId', { roleId })
    }

    // 分页
    const total = await queryBuilder.getCount()
    const users = await queryBuilder
      .orderBy('user.createdAt', 'DESC')
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getMany()

    return { users, total }
  }

  async findOne(id: string, tenantId: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id, tenantId, deletedAt: null },
      relations: ['roles', 'roles.permissions']
    })

    if (!user) {
      throw new NotFoundException('用户不存在')
    }

    return user
  }

  async findByEmail(email: string, tenantId: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { email, tenantId, deletedAt: null },
      relations: ['roles']
    })
  }

  async findByUsername(username: string, tenantId: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { username, tenantId, deletedAt: null },
      relations: ['roles']
    })
  }

  async update(id: string, updateUserDto: UpdateUserDto, tenantId: string): Promise<User> {
    const user = await this.findOne(id, tenantId)

    // 检查用户名和邮箱冲突
    if (updateUserDto.username || updateUserDto.email) {
      const conflictUser = await this.userRepository.findOne({
        where: [
          { username: updateUserDto.username, tenantId, id: Not(id) },
          { email: updateUserDto.email, tenantId, id: Not(id) }
        ]
      })

      if (conflictUser) {
        throw new ConflictException('用户名或邮箱已存在')
      }
    }

    // 更新密码
    if (updateUserDto.password) {
      updateUserDto.password = await bcrypt.hash(updateUserDto.password, 10)
    }

    // 更新角色
    if (updateUserDto.roleIds) {
      const roles = await this.roleRepository.findByIds(updateUserDto.roleIds)
      user.roles = roles
    }

    // 更新用户信息
    Object.assign(user, updateUserDto)
    
    return this.userRepository.save(user)
  }

  async remove(id: string, tenantId: string): Promise<void> {
    const user = await this.findOne(id, tenantId)
    
    // 软删除
    user.deletedAt = new Date()
    await this.userRepository.save(user)
  }

  async changePassword(id: string, oldPassword: string, newPassword: string, tenantId: string): Promise<void> {
    const user = await this.userRepository.findOne({
      where: { id, tenantId, deletedAt: null },
      select: ['id', 'password']
    })

    if (!user) {
      throw new NotFoundException('用户不存在')
    }

    // 验证旧密码
    const isOldPasswordValid = await bcrypt.compare(oldPassword, user.password)
    if (!isOldPasswordValid) {
      throw new BadRequestException('原密码错误')
    }

    // 更新密码
    const hashedNewPassword = await bcrypt.hash(newPassword, 10)
    await this.userRepository.update(id, { password: hashedNewPassword })
  }

  async updateStatus(id: string, status: UserStatus, tenantId: string): Promise<User> {
    const user = await this.findOne(id, tenantId)
    user.status = status
    return this.userRepository.save(user)
  }

  async updateLastLogin(id: string, ip: string): Promise<void> {
    await this.userRepository.update(id, {
      lastLoginAt: new Date(),
      lastLoginIp: ip
    })
  }

  async validatePassword(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword)
  }

  async getStatistics(tenantId: string): Promise<any> {
    const total = await this.userRepository.count({
      where: { tenantId, deletedAt: null }
    })

    const active = await this.userRepository.count({
      where: { tenantId, status: UserStatus.ACTIVE, deletedAt: null }
    })

    const inactive = await this.userRepository.count({
      where: { tenantId, status: UserStatus.INACTIVE, deletedAt: null }
    })

    const suspended = await this.userRepository.count({
      where: { tenantId, status: UserStatus.SUSPENDED, deletedAt: null }
    })

    return {
      total,
      active,
      inactive,
      suspended
    }
  }
}
