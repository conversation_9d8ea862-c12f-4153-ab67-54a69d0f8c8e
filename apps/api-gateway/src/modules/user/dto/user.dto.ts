import { IsString, IsEmail, IsOptional, IsEnum, IsArray, IsUUID, MinLength, MaxLength, IsPhoneNumber, IsObject } from 'class-validator'
import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger'
import { Transform, Type } from 'class-transformer'
import { UserStatus } from '../entities/user.entity'

export class CreateUserDto {
  @ApiProperty({ description: '用户名', example: 'john_doe' })
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  username: string

  @ApiProperty({ description: '邮箱', example: '<EMAIL>' })
  @IsEmail()
  @MaxLength(100)
  email: string

  @ApiProperty({ description: '密码', example: 'password123' })
  @IsString()
  @MinLength(6)
  @MaxLength(50)
  password: string

  @ApiPropertyOptional({ description: '手机号', example: '+86 13800138000' })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  phone?: string

  @ApiPropertyOptional({ description: '头像URL', example: 'https://example.com/avatar.jpg' })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  avatar?: string

  @ApiPropertyOptional({ description: '用户状态', enum: UserStatus, example: UserStatus.ACTIVE })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus

  @ApiPropertyOptional({ description: '角色ID列表', type: [String] })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  roleIds?: string[]

  @ApiPropertyOptional({ description: '用户资料', type: 'object' })
  @IsOptional()
  @IsObject()
  profile?: Record<string, any>

  @ApiPropertyOptional({ description: '用户设置', type: 'object' })
  @IsOptional()
  @IsObject()
  settings?: Record<string, any>
}

export class UpdateUserDto extends PartialType(CreateUserDto) {
  @ApiPropertyOptional({ description: '密码（可选）' })
  @IsOptional()
  @IsString()
  @MinLength(6)
  @MaxLength(50)
  password?: string
}

export class QueryUserDto {
  @ApiPropertyOptional({ description: '页码', example: 1 })
  @IsOptional()
  @Type(() => Number)
  page?: number = 1

  @ApiPropertyOptional({ description: '每页数量', example: 10 })
  @IsOptional()
  @Type(() => Number)
  pageSize?: number = 10

  @ApiPropertyOptional({ description: '搜索关键词' })
  @IsOptional()
  @IsString()
  search?: string

  @ApiPropertyOptional({ description: '用户状态', enum: UserStatus })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus

  @ApiPropertyOptional({ description: '角色ID' })
  @IsOptional()
  @IsUUID()
  roleId?: string

  @ApiPropertyOptional({ description: '排序字段', example: 'createdAt' })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt'

  @ApiPropertyOptional({ description: '排序方向', example: 'DESC' })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC'
}

export class ChangePasswordDto {
  @ApiProperty({ description: '原密码' })
  @IsString()
  @MinLength(6)
  oldPassword: string

  @ApiProperty({ description: '新密码' })
  @IsString()
  @MinLength(6)
  @MaxLength(50)
  newPassword: string
}

export class UpdateUserStatusDto {
  @ApiProperty({ description: '用户状态', enum: UserStatus })
  @IsEnum(UserStatus)
  status: UserStatus
}

export class UserResponseDto {
  @ApiProperty({ description: '用户ID' })
  id: string

  @ApiProperty({ description: '用户名' })
  username: string

  @ApiProperty({ description: '邮箱' })
  email: string

  @ApiPropertyOptional({ description: '手机号' })
  phone?: string

  @ApiPropertyOptional({ description: '头像URL' })
  avatar?: string

  @ApiProperty({ description: '用户状态', enum: UserStatus })
  status: UserStatus

  @ApiPropertyOptional({ description: '最后登录时间' })
  lastLoginAt?: Date

  @ApiPropertyOptional({ description: '最后登录IP' })
  lastLoginIp?: string

  @ApiPropertyOptional({ description: '用户资料' })
  profile?: Record<string, any>

  @ApiPropertyOptional({ description: '用户设置' })
  settings?: Record<string, any>

  @ApiProperty({ description: '创建时间' })
  createdAt: Date

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date

  @ApiPropertyOptional({ description: '角色列表' })
  roles?: any[]

  @ApiProperty({ description: '显示名称' })
  displayName: string

  @ApiProperty({ description: '是否激活' })
  isActive: boolean
}

export class UserListResponseDto {
  @ApiProperty({ description: '用户列表', type: [UserResponseDto] })
  users: UserResponseDto[]

  @ApiProperty({ description: '总数' })
  total: number

  @ApiProperty({ description: '当前页' })
  page: number

  @ApiProperty({ description: '每页数量' })
  pageSize: number

  @ApiProperty({ description: '总页数' })
  totalPages: number
}

export class UserStatisticsDto {
  @ApiProperty({ description: '总用户数' })
  total: number

  @ApiProperty({ description: '活跃用户数' })
  active: number

  @ApiProperty({ description: '非活跃用户数' })
  inactive: number

  @ApiProperty({ description: '已暂停用户数' })
  suspended: number
}
