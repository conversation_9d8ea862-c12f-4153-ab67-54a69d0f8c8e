import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'

import { UserController } from './controllers/user.controller'
import { UserService } from './services/user.service'
import { TenantService } from './services/tenant.service'

import { User } from './entities/user.entity'
import { Role } from './entities/role.entity'
import { Tenant } from './entities/tenant.entity'

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Role, Tenant])
  ],
  controllers: [UserController],
  providers: [UserService, TenantService],
  exports: [UserService, TenantService]
})
export class UserModule {}
