import { Injectable, NotFoundException, ConflictException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { MembershipPlan, PlanStatus, PlanType, BillingCycle } from '../entities/membership-plan.entity'

@Injectable()
export class MembershipPlanService {
  constructor(
    @InjectRepository(MembershipPlan)
    private planRepository: Repository<MembershipPlan>
  ) {}

  async create(createPlanDto: {
    name: string
    code: string
    description?: string
    type: PlanType
    billingCycle: BillingCycle
    price: number
    originalPrice?: number
    currency?: string
    trialDays?: number
    features?: any
    limits?: any
  }): Promise<MembershipPlan> {
    // 检查计划代码是否已存在
    const existingPlan = await this.planRepository.findOne({
      where: { code: createPlanDto.code }
    })

    if (existingPlan) {
      throw new ConflictException('计划代码已存在')
    }

    const plan = this.planRepository.create({
      ...createPlanDto,
      status: PlanStatus.ACTIVE,
      currency: createPlanDto.currency || 'CNY'
    })

    return this.planRepository.save(plan)
  }

  async findAll(includeInactive = false): Promise<MembershipPlan[]> {
    const queryBuilder = this.planRepository
      .createQueryBuilder('plan')
      .where('plan.deletedAt IS NULL')
      .orderBy('plan.sortOrder', 'ASC')
      .addOrderBy('plan.createdAt', 'ASC')

    if (!includeInactive) {
      queryBuilder.andWhere('plan.status = :status', { status: PlanStatus.ACTIVE })
    }

    return queryBuilder.getMany()
  }

  async findByCode(code: string): Promise<MembershipPlan | null> {
    return this.planRepository.findOne({
      where: { code, deletedAt: null }
    })
  }

  async findById(id: string): Promise<MembershipPlan | null> {
    return this.planRepository.findOne({
      where: { id, deletedAt: null }
    })
  }

  async update(id: string, updatePlanDto: Partial<MembershipPlan>): Promise<MembershipPlan> {
    const plan = await this.findById(id)
    
    if (!plan) {
      throw new NotFoundException('计划不存在')
    }

    // 如果更新代码，检查是否冲突
    if (updatePlanDto.code && updatePlanDto.code !== plan.code) {
      const existingPlan = await this.planRepository.findOne({
        where: { code: updatePlanDto.code }
      })

      if (existingPlan) {
        throw new ConflictException('计划代码已存在')
      }
    }

    Object.assign(plan, updatePlanDto)
    return this.planRepository.save(plan)
  }

  async activate(id: string): Promise<MembershipPlan> {
    const plan = await this.findById(id)
    
    if (!plan) {
      throw new NotFoundException('计划不存在')
    }

    plan.status = PlanStatus.ACTIVE
    return this.planRepository.save(plan)
  }

  async deactivate(id: string): Promise<MembershipPlan> {
    const plan = await this.findById(id)
    
    if (!plan) {
      throw new NotFoundException('计划不存在')
    }

    plan.status = PlanStatus.INACTIVE
    return this.planRepository.save(plan)
  }

  async archive(id: string): Promise<MembershipPlan> {
    const plan = await this.findById(id)
    
    if (!plan) {
      throw new NotFoundException('计划不存在')
    }

    plan.status = PlanStatus.ARCHIVED
    return this.planRepository.save(plan)
  }

  async remove(id: string): Promise<void> {
    const plan = await this.findById(id)
    
    if (!plan) {
      throw new NotFoundException('计划不存在')
    }

    // 软删除
    plan.deletedAt = new Date()
    await this.planRepository.save(plan)
  }

  async getPublicPlans(): Promise<MembershipPlan[]> {
    return this.planRepository.find({
      where: {
        status: PlanStatus.ACTIVE,
        deletedAt: null
      },
      order: {
        sortOrder: 'ASC',
        createdAt: 'ASC'
      }
    })
  }

  async createDefaultPlans(): Promise<void> {
    const defaultPlans = [
      {
        name: '免费版',
        code: 'free',
        description: '基础功能，适合个人用户',
        type: PlanType.FREE,
        billingCycle: BillingCycle.MONTHLY,
        price: 0,
        trialDays: 0,
        sortOrder: 1,
        features: {
          included: ['basic_crm', 'basic_reports'],
          excluded: ['ai_writing', 'ai_analytics', 'advanced_reports']
        },
        limits: {
          maxUsers: 1,
          maxStorage: 100, // 100MB
          maxProjects: 3,
          maxAIRequests: 10,
          maxCustomers: 100
        }
      },
      {
        name: '基础版',
        code: 'basic',
        description: '适合小团队使用',
        type: PlanType.BASIC,
        billingCycle: BillingCycle.MONTHLY,
        price: 99,
        originalPrice: 129,
        trialDays: 7,
        sortOrder: 2,
        features: {
          included: ['basic_crm', 'basic_reports', 'ai_writing', 'email_integration'],
          excluded: ['ai_analytics', 'advanced_reports', 'api_access']
        },
        limits: {
          maxUsers: 5,
          maxStorage: 1024, // 1GB
          maxProjects: 10,
          maxAIRequests: 100,
          maxCustomers: 1000
        }
      },
      {
        name: '专业版',
        code: 'pro',
        description: '适合中小企业',
        type: PlanType.PRO,
        billingCycle: BillingCycle.MONTHLY,
        price: 299,
        originalPrice: 399,
        trialDays: 14,
        sortOrder: 3,
        features: {
          included: [
            'advanced_crm', 'advanced_reports', 'ai_writing', 'ai_analytics',
            'email_integration', 'sms_integration', 'api_access'
          ]
        },
        limits: {
          maxUsers: 20,
          maxStorage: 10240, // 10GB
          maxProjects: 50,
          maxAIRequests: 1000,
          maxCustomers: 10000
        }
      },
      {
        name: '企业版',
        code: 'enterprise',
        description: '适合大型企业',
        type: PlanType.ENTERPRISE,
        billingCycle: BillingCycle.YEARLY,
        price: 2999,
        originalPrice: 3999,
        trialDays: 30,
        sortOrder: 4,
        features: {
          included: [
            'advanced_crm', 'advanced_reports', 'ai_writing', 'ai_analytics',
            'ai_agents', 'email_integration', 'sms_integration', 'api_access',
            'custom_plugins', 'white_label', 'sso', 'priority_support'
          ]
        },
        limits: {
          maxUsers: -1, // 无限制
          maxStorage: -1, // 无限制
          maxProjects: -1,
          maxAIRequests: -1,
          maxCustomers: -1
        }
      }
    ]

    for (const planData of defaultPlans) {
      const existingPlan = await this.findByCode(planData.code)
      if (!existingPlan) {
        await this.create(planData)
      }
    }
  }
}
