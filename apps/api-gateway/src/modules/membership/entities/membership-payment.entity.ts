import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index
} from 'typeorm'
import { User } from '../../user/entities/user.entity'
import { MembershipSubscription } from './membership-subscription.entity'

export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded'
}

export enum PaymentMethod {
  ALIPAY = 'alipay',
  WECHAT = 'wechat',
  BANK_CARD = 'bank_card',
  PAYPAL = 'paypal',
  STRIPE = 'stripe',
  MANUAL = 'manual'
}

@Entity('membership_payments')
@Index(['userId', 'status'])
@Index(['subscriptionId'])
@Index(['transactionId'], { unique: true })
export class MembershipPayment {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ name: 'user_id' })
  userId: string

  @Column({ name: 'subscription_id' })
  subscriptionId: string

  @Column({ name: 'transaction_id', unique: true })
  transactionId: string

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number

  @Column({ length: 3, default: 'CNY' })
  currency: string

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING
  })
  status: PaymentStatus

  @Column({
    type: 'enum',
    enum: PaymentMethod
  })
  method: PaymentMethod

  @Column({ name: 'payment_gateway', length: 50, nullable: true })
  paymentGateway?: string

  @Column({ name: 'gateway_transaction_id', nullable: true })
  gatewayTransactionId?: string

  @Column({ name: 'paid_at', nullable: true })
  paidAt?: Date

  @Column({ name: 'failed_at', nullable: true })
  failedAt?: Date

  @Column({ name: 'failure_reason', length: 500, nullable: true })
  failureReason?: string

  @Column({ name: 'refunded_at', nullable: true })
  refundedAt?: Date

  @Column({ name: 'refund_amount', type: 'decimal', precision: 10, scale: 2, nullable: true })
  refundAmount?: number

  @Column({ name: 'refund_reason', length: 500, nullable: true })
  refundReason?: string

  @Column({ type: 'jsonb', nullable: true })
  gatewayResponse?: Record<string, any>

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date

  // 关联关系
  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User

  @ManyToOne(() => MembershipSubscription, subscription => subscription.payments)
  @JoinColumn({ name: 'subscription_id' })
  subscription: MembershipSubscription

  // 虚拟属性
  get isCompleted(): boolean {
    return this.status === PaymentStatus.COMPLETED
  }

  get isFailed(): boolean {
    return this.status === PaymentStatus.FAILED
  }

  get isPending(): boolean {
    return this.status === PaymentStatus.PENDING
  }

  get isRefunded(): boolean {
    return this.status === PaymentStatus.REFUNDED
  }

  get canRefund(): boolean {
    return this.status === PaymentStatus.COMPLETED && !this.refundedAt
  }

  // 方法
  markAsCompleted(gatewayTransactionId?: string, gatewayResponse?: Record<string, any>): void {
    this.status = PaymentStatus.COMPLETED
    this.paidAt = new Date()
    this.gatewayTransactionId = gatewayTransactionId
    this.gatewayResponse = gatewayResponse
  }

  markAsFailed(reason: string, gatewayResponse?: Record<string, any>): void {
    this.status = PaymentStatus.FAILED
    this.failedAt = new Date()
    this.failureReason = reason
    this.gatewayResponse = gatewayResponse
  }

  markAsRefunded(amount: number, reason?: string): void {
    this.status = PaymentStatus.REFUNDED
    this.refundedAt = new Date()
    this.refundAmount = amount
    this.refundReason = reason
  }

  cancel(): void {
    this.status = PaymentStatus.CANCELLED
  }
}
