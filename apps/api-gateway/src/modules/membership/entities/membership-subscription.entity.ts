import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
  Index
} from 'typeorm'
import { User } from '../../user/entities/user.entity'
import { MembershipPlan } from './membership-plan.entity'
import { MembershipPayment } from './membership-payment.entity'

export enum SubscriptionStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
  TRIAL = 'trial',
  PAST_DUE = 'past_due'
}

@Entity('membership_subscriptions')
@Index(['userId', 'status'])
@Index(['planId', 'status'])
export class MembershipSubscription {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ name: 'user_id' })
  userId: string

  @Column({ name: 'plan_id' })
  planId: string

  @Column({
    type: 'enum',
    enum: SubscriptionStatus,
    default: SubscriptionStatus.ACTIVE
  })
  status: SubscriptionStatus

  @Column({ name: 'start_date' })
  startDate: Date

  @Column({ name: 'end_date', nullable: true })
  endDate?: Date

  @Column({ name: 'trial_end_date', nullable: true })
  trialEndDate?: Date

  @Column({ name: 'next_billing_date', nullable: true })
  nextBillingDate?: Date

  @Column({ name: 'cancelled_at', nullable: true })
  cancelledAt?: Date

  @Column({ name: 'cancellation_reason', length: 500, nullable: true })
  cancellationReason?: string

  @Column({ name: 'auto_renew', default: true })
  autoRenew: boolean

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date

  // 关联关系
  @ManyToOne(() => User, user => user.subscriptions)
  @JoinColumn({ name: 'user_id' })
  user: User

  @ManyToOne(() => MembershipPlan, plan => plan.subscriptions)
  @JoinColumn({ name: 'plan_id' })
  plan: MembershipPlan

  @OneToMany(() => MembershipPayment, payment => payment.subscription)
  payments: MembershipPayment[]

  // 虚拟属性
  get isActive(): boolean {
    const now = new Date()
    return (
      this.status === SubscriptionStatus.ACTIVE &&
      (!this.endDate || this.endDate > now)
    )
  }

  get isTrial(): boolean {
    const now = new Date()
    return (
      this.status === SubscriptionStatus.TRIAL ||
      (this.trialEndDate && this.trialEndDate > now)
    )
  }

  get isExpired(): boolean {
    const now = new Date()
    return this.endDate && this.endDate <= now
  }

  get daysUntilExpiry(): number {
    if (!this.endDate) return -1
    const now = new Date()
    const diffTime = this.endDate.getTime() - now.getTime()
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  get daysUntilTrialEnd(): number {
    if (!this.trialEndDate) return -1
    const now = new Date()
    const diffTime = this.trialEndDate.getTime() - now.getTime()
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  // 方法
  canRenew(): boolean {
    return this.status === SubscriptionStatus.ACTIVE && this.autoRenew
  }

  canCancel(): boolean {
    return [
      SubscriptionStatus.ACTIVE,
      SubscriptionStatus.TRIAL
    ].includes(this.status)
  }

  canReactivate(): boolean {
    return [
      SubscriptionStatus.CANCELLED,
      SubscriptionStatus.EXPIRED
    ].includes(this.status)
  }

  cancel(reason?: string): void {
    this.status = SubscriptionStatus.CANCELLED
    this.cancelledAt = new Date()
    this.cancellationReason = reason
    this.autoRenew = false
  }

  renew(endDate: Date): void {
    this.status = SubscriptionStatus.ACTIVE
    this.endDate = endDate
    this.nextBillingDate = endDate
  }

  expire(): void {
    this.status = SubscriptionStatus.EXPIRED
  }

  activate(): void {
    this.status = SubscriptionStatus.ACTIVE
    this.cancelledAt = null
    this.cancellationReason = null
  }
}
