import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
  Index
} from 'typeorm'
import { User } from '../../user/entities/user.entity'
import { CourseLesson } from './course-lesson.entity'
import { CourseEnrollment } from './course-enrollment.entity'

export enum CourseStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived'
}

export enum CourseLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced'
}

export enum CourseType {
  FREE = 'free',
  PAID = 'paid',
  MEMBERSHIP = 'membership'
}

@Entity('courses')
@Index(['status', 'type'])
@Index(['createdBy'])
export class Course {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ length: 200 })
  title: string

  @Column({ length: 500, nullable: true })
  subtitle?: string

  @Column({ type: 'text', nullable: true })
  description?: string

  @Column({ length: 255, nullable: true })
  coverImage?: string

  @Column({
    type: 'enum',
    enum: CourseStatus,
    default: CourseStatus.DRAFT
  })
  status: CourseStatus

  @Column({
    type: 'enum',
    enum: CourseLevel,
    default: CourseLevel.BEGINNER
  })
  level: CourseLevel

  @Column({
    type: 'enum',
    enum: CourseType,
    default: CourseType.FREE
  })
  type: CourseType

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  price: number

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  originalPrice?: number

  @Column({ length: 3, default: 'CNY' })
  currency: string

  @Column({ type: 'int', default: 0 })
  duration: number // 课程总时长(分钟)

  @Column({ type: 'int', default: 0 })
  lessonCount: number // 课程数量

  @Column({ type: 'int', default: 0 })
  enrollmentCount: number // 报名人数

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0 })
  rating: number // 评分

  @Column({ type: 'int', default: 0 })
  reviewCount: number // 评价数量

  @Column({ type: 'simple-array', nullable: true })
  tags?: string[]

  @Column({ type: 'simple-array', nullable: true })
  categories?: string[]

  @Column({ type: 'jsonb', nullable: true })
  requirements?: string[] // 学习要求

  @Column({ type: 'jsonb', nullable: true })
  objectives?: string[] // 学习目标

  @Column({ name: 'created_by' })
  createdBy: string

  @Column({ type: 'int', default: 0 })
  sortOrder: number

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date

  @Column({ name: 'deleted_at', nullable: true })
  deletedAt?: Date

  // 关联关系
  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User

  @OneToMany(() => CourseLesson, lesson => lesson.course)
  lessons: CourseLesson[]

  @OneToMany(() => CourseEnrollment, enrollment => enrollment.course)
  enrollments: CourseEnrollment[]

  // 虚拟属性
  get isPublished(): boolean {
    return this.status === CourseStatus.PUBLISHED && !this.deletedAt
  }

  get isFree(): boolean {
    return this.type === CourseType.FREE || this.price === 0
  }

  get hasDiscount(): boolean {
    return this.originalPrice && this.originalPrice > this.price
  }

  get discountPercentage(): number {
    if (!this.hasDiscount) return 0
    return Math.round(((this.originalPrice - this.price) / this.originalPrice) * 100)
  }

  get averageRating(): number {
    return this.reviewCount > 0 ? this.rating : 0
  }

  // 方法
  publish(): void {
    this.status = CourseStatus.PUBLISHED
  }

  archive(): void {
    this.status = CourseStatus.ARCHIVED
  }

  updateStats(lessonCount: number, duration: number): void {
    this.lessonCount = lessonCount
    this.duration = duration
  }

  updateRating(newRating: number, reviewCount: number): void {
    this.rating = newRating
    this.reviewCount = reviewCount
  }

  incrementEnrollment(): void {
    this.enrollmentCount += 1
  }

  decrementEnrollment(): void {
    if (this.enrollmentCount > 0) {
      this.enrollmentCount -= 1
    }
  }
}
