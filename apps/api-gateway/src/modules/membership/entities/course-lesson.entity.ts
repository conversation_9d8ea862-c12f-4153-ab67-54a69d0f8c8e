import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
  Index
} from 'typeorm'
import { Course } from './course.entity'
import { LessonProgress } from './lesson-progress.entity'

export enum LessonType {
  VIDEO = 'video',
  AUDIO = 'audio',
  TEXT = 'text',
  QUIZ = 'quiz',
  ASSIGNMENT = 'assignment',
  LIVE = 'live'
}

export enum LessonStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived'
}

@Entity('course_lessons')
@Index(['courseId', 'sortOrder'])
@Index(['status'])
export class CourseLesson {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ name: 'course_id' })
  courseId: string

  @Column({ length: 200 })
  title: string

  @Column({ type: 'text', nullable: true })
  description?: string

  @Column({
    type: 'enum',
    enum: LessonType,
    default: LessonType.VIDEO
  })
  type: LessonType

  @Column({
    type: 'enum',
    enum: LessonStatus,
    default: LessonStatus.DRAFT
  })
  status: LessonStatus

  @Column({ type: 'int', default: 0 })
  duration: number // 时长(秒)

  @Column({ type: 'int', default: 0 })
  sortOrder: number

  @Column({ name: 'is_free', default: false })
  isFree: boolean

  @Column({ name: 'video_url', nullable: true })
  videoUrl?: string

  @Column({ name: 'audio_url', nullable: true })
  audioUrl?: string

  @Column({ type: 'text', nullable: true })
  content?: string // 文本内容或HTML

  @Column({ type: 'jsonb', nullable: true })
  resources?: LessonResource[] // 课程资源

  @Column({ type: 'jsonb', nullable: true })
  quiz?: QuizData // 测验数据

  @Column({ type: 'jsonb', nullable: true })
  assignment?: AssignmentData // 作业数据

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date

  @Column({ name: 'deleted_at', nullable: true })
  deletedAt?: Date

  // 关联关系
  @ManyToOne(() => Course, course => course.lessons)
  @JoinColumn({ name: 'course_id' })
  course: Course

  @OneToMany(() => LessonProgress, progress => progress.lesson)
  progresses: LessonProgress[]

  // 虚拟属性
  get isPublished(): boolean {
    return this.status === LessonStatus.PUBLISHED && !this.deletedAt
  }

  get durationInMinutes(): number {
    return Math.ceil(this.duration / 60)
  }

  // 方法
  publish(): void {
    this.status = LessonStatus.PUBLISHED
  }

  archive(): void {
    this.status = LessonStatus.ARCHIVED
  }

  addResource(resource: LessonResource): void {
    if (!this.resources) {
      this.resources = []
    }
    this.resources.push(resource)
  }

  removeResource(resourceId: string): void {
    if (this.resources) {
      this.resources = this.resources.filter(r => r.id !== resourceId)
    }
  }
}

export interface LessonResource {
  id: string
  name: string
  type: 'pdf' | 'doc' | 'image' | 'link' | 'other'
  url: string
  size?: number
  description?: string
}

export interface QuizData {
  questions: QuizQuestion[]
  passingScore: number
  timeLimit?: number // 时间限制(分钟)
  allowRetry: boolean
  showCorrectAnswers: boolean
}

export interface QuizQuestion {
  id: string
  type: 'single' | 'multiple' | 'text' | 'true_false'
  question: string
  options?: string[]
  correctAnswers: string[]
  explanation?: string
  points: number
}

export interface AssignmentData {
  title: string
  description: string
  instructions: string
  dueDate?: Date
  maxScore: number
  submissionFormat: 'text' | 'file' | 'both'
  allowLateSubmission: boolean
}
