import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index
} from 'typeorm'
import { MembershipSubscription } from './membership-subscription.entity'

export enum PlanType {
  FREE = 'free',
  BASIC = 'basic',
  PRO = 'pro',
  ENTERPRISE = 'enterprise'
}

export enum PlanStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ARCHIVED = 'archived'
}

export enum BillingCycle {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
  LIFETIME = 'lifetime'
}

@Entity('membership_plans')
@Index(['code'], { unique: true })
export class MembershipPlan {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ length: 100 })
  name: string

  @Column({ length: 50, unique: true })
  code: string

  @Column({ length: 500, nullable: true })
  description?: string

  @Column({
    type: 'enum',
    enum: PlanType,
    default: PlanType.FREE
  })
  type: PlanType

  @Column({
    type: 'enum',
    enum: PlanStatus,
    default: PlanStatus.ACTIVE
  })
  status: PlanStatus

  @Column({
    type: 'enum',
    enum: BillingCycle,
    default: BillingCycle.MONTHLY
  })
  billingCycle: BillingCycle

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  price: number

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  originalPrice?: number

  @Column({ length: 3, default: 'CNY' })
  currency: string

  @Column({ type: 'int', nullable: true })
  trialDays?: number

  @Column({ type: 'int', default: 0 })
  sortOrder: number

  @Column({ type: 'jsonb', nullable: true })
  features?: PlanFeatures

  @Column({ type: 'jsonb', nullable: true })
  limits?: PlanLimits

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date

  @Column({ name: 'deleted_at', nullable: true })
  deletedAt?: Date

  // 关联关系
  @OneToMany(() => MembershipSubscription, subscription => subscription.plan)
  subscriptions: MembershipSubscription[]

  // 虚拟属性
  get isActive(): boolean {
    return this.status === PlanStatus.ACTIVE && !this.deletedAt
  }

  get isFree(): boolean {
    return this.type === PlanType.FREE || this.price === 0
  }

  get hasDiscount(): boolean {
    return this.originalPrice && this.originalPrice > this.price
  }

  get discountPercentage(): number {
    if (!this.hasDiscount) return 0
    return Math.round(((this.originalPrice - this.price) / this.originalPrice) * 100)
  }

  // 方法
  hasFeature(feature: string): boolean {
    return this.features?.included?.includes(feature) || false
  }

  getLimit(limitType: string): number {
    return this.limits?.[limitType] || 0
  }

  isWithinLimit(limitType: string, currentUsage: number): boolean {
    const limit = this.getLimit(limitType)
    return limit === -1 || currentUsage < limit // -1 表示无限制
  }
}

export interface PlanFeatures {
  included: string[] // 包含的功能列表
  excluded?: string[] // 排除的功能列表
  beta?: string[] // 测试功能
}

export interface PlanLimits {
  maxUsers?: number // 最大用户数
  maxStorage?: number // 最大存储空间(MB)
  maxProjects?: number // 最大项目数
  maxAIRequests?: number // 每月AI请求次数
  maxCustomers?: number // 最大客户数
  maxOrders?: number // 每月最大订单数
  maxIntegrations?: number // 最大集成数
  [key: string]: number // 其他限制
}
