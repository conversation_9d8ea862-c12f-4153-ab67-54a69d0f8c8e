import { Controller, Get } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger'
import { AppService } from './app.service'
import { Public } from './auth/auth.controller'

@ApiTags('系统')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @Public()
  @ApiOperation({ summary: '获取系统信息' })
  @ApiResponse({
    status: 200,
    description: '系统信息获取成功'
  })
  getSystemInfo(): {
    name: string
    version: string
    description: string
    status: string
    timestamp: number
  } {
    return this.appService.getSystemInfo()
  }

  @Get('health')
  @Public()
  @ApiOperation({ summary: '健康检查' })
  @ApiResponse({
    status: 200,
    description: '系统健康'
  })
  healthCheck(): {
    status: string
    timestamp: number
    uptime: number
  } {
    return this.appService.healthCheck()
  }
}
