import {
  Controller,
  Post,
  Body,
  HttpStatus,
  UseGuards,
  Request,
  Get,
  SetMetadata
} from '@nestjs/common'
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth
} from '@nestjs/swagger'
import { AuthService } from './auth.service'
import { JwtAuthGuard } from './guards/jwt-auth.guard'
import { CurrentUser } from './decorators/current-user.decorator'
import {
  LoginDto,
  RegisterDto,
  RefreshTokenDto,
  LogoutDto,
  AuthResponseDto
} from './dto/auth.dto'
import { User } from '../modules/user/entities/user.entity'

export const Public = () => SetMetadata('isPublic', true)

@ApiTags('认证')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @Public()
  @ApiOperation({ summary: '用户登录' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '登录成功',
    type: AuthResponseDto
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: '用户名或密码错误'
  })
  async login(
    @Body() loginDto: LoginDto,
    @Request() req: any
  ): Promise<AuthResponseDto> {
    const ip = req.ip || req.connection.remoteAddress || '127.0.0.1'
    return this.authService.login(loginDto, ip)
  }

  @Post('register')
  @Public()
  @ApiOperation({ summary: '用户注册' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '注册成功',
    type: AuthResponseDto
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: '用户名或邮箱已存在'
  })
  async register(@Body() registerDto: RegisterDto): Promise<AuthResponseDto> {
    return this.authService.register(registerDto)
  }

  @Post('refresh')
  @Public()
  @ApiOperation({ summary: '刷新访问令牌' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '令牌刷新成功',
    type: AuthResponseDto
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: '无效的刷新令牌'
  })
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto): Promise<AuthResponseDto> {
    return this.authService.refreshToken(refreshTokenDto.refreshToken)
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '用户登出' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '登出成功'
  })
  async logout(
    @CurrentUser() user: User,
    @Body() logoutDto: LogoutDto
  ): Promise<{ message: string }> {
    await this.authService.logout(user.id, logoutDto.refreshToken)
    return { message: '登出成功' }
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取当前用户信息' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取用户信息成功'
  })
  async getProfile(@CurrentUser() user: User): Promise<{
    id: string
    username: string
    email: string
    avatar?: string
    roles: string[]
    permissions: string[]
  }> {
    const permissions = []
    
    // 收集用户所有权限
    if (user.roles) {
      for (const role of user.roles) {
        if (role.permissions) {
          for (const permission of role.permissions) {
            permissions.push(`${permission.resource}:${permission.action}`)
          }
        }
      }
    }

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      avatar: user.avatar,
      roles: user.roles?.map(role => role.code) || [],
      permissions: [...new Set(permissions)] // 去重
    }
  }

  @Get('check')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '检查令牌有效性' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '令牌有效'
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: '令牌无效'
  })
  async checkToken(): Promise<{ valid: boolean; message: string }> {
    return {
      valid: true,
      message: '令牌有效'
    }
  }
}
