import { Injectable, UnauthorizedException } from '@nestjs/common'
import { PassportStrategy } from '@nestjs/passport'
import { ExtractJwt, Strategy } from 'passport-jwt'
import { ConfigService } from '@nestjs/config'
import { UserService } from '../../modules/user/services/user.service'

export interface JwtPayload {
  sub: string // user id
  username: string
  email: string
  tenantId: string
  iat?: number
  exp?: number
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService,
    private userService: UserService
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET')
    })
  }

  async validate(payload: JwtPayload) {
    const user = await this.userService.findOne(payload.sub, payload.tenantId)
    
    if (!user) {
      throw new UnauthorizedException('用户不存在')
    }

    if (!user.isActive) {
      throw new UnauthorizedException('用户账户已被禁用')
    }

    return user
  }
}
