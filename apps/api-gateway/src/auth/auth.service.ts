import { Injectable, UnauthorizedException, ConflictException, NotFoundException } from '@nestjs/common'
import { JwtService } from '@nestjs/jwt'
import { ConfigService } from '@nestjs/config'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import * as bcrypt from 'bcryptjs'
import { UserService } from '../modules/user/services/user.service'
import { TenantService } from '../modules/user/services/tenant.service'
import { User } from '../modules/user/entities/user.entity'
import { Tenant } from '../modules/user/entities/tenant.entity'
import { LoginDto, RegisterDto, AuthResponseDto } from './dto/auth.dto'
import { JwtPayload } from './strategies/jwt.strategy'

@Injectable()
export class AuthService {
  constructor(
    private userService: UserService,
    private tenantService: TenantService,
    private jwtService: JwtService,
    private configService: ConfigService,
    @InjectRepository(User)
    private userRepository: Repository<User>
  ) {}

  async login(loginDto: LoginDto, ip: string): Promise<AuthResponseDto> {
    // 获取租户
    const tenant = await this.getTenant(loginDto.tenantCode)
    
    // 查找用户（支持用户名或邮箱登录）
    const user = await this.findUserByUsernameOrEmail(loginDto.username, tenant.id)
    
    if (!user) {
      throw new UnauthorizedException('用户名或密码错误')
    }

    // 验证密码
    const isPasswordValid = await this.userService.validatePassword(
      loginDto.password,
      user.password
    )

    if (!isPasswordValid) {
      throw new UnauthorizedException('用户名或密码错误')
    }

    // 检查用户状态
    if (!user.isActive) {
      throw new UnauthorizedException('用户账户已被禁用')
    }

    // 更新最后登录信息
    await this.userService.updateLastLogin(user.id, ip)

    // 生成令牌
    const tokens = await this.generateTokens(user)

    return {
      ...tokens,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        avatar: user.avatar,
        roles: user.roles?.map(role => role.code) || []
      }
    }
  }

  async register(registerDto: RegisterDto): Promise<AuthResponseDto> {
    // 获取或创建租户
    let tenant = await this.tenantService.findByCode(registerDto.tenantCode || 'default')
    
    if (!tenant) {
      // 如果是默认租户且不存在，则创建
      if (registerDto.tenantCode === 'default' || !registerDto.tenantCode) {
        tenant = await this.tenantService.createDefaultTenant()
      } else {
        throw new NotFoundException('租户不存在')
      }
    }

    // 检查租户是否可以创建新用户
    if (!tenant.canCreateUser()) {
      throw new ConflictException('租户用户数量已达上限')
    }

    // 创建用户
    const user = await this.userService.create({
      username: registerDto.username,
      email: registerDto.email,
      password: registerDto.password
    }, tenant.id)

    // 生成令牌
    const tokens = await this.generateTokens(user)

    return {
      ...tokens,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        avatar: user.avatar,
        roles: user.roles?.map(role => role.code) || []
      }
    }
  }

  async refreshToken(refreshToken: string): Promise<AuthResponseDto> {
    try {
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get<string>('JWT_REFRESH_SECRET')
      })

      const user = await this.userService.findOne(payload.sub, payload.tenantId)
      
      if (!user || !user.isActive) {
        throw new UnauthorizedException('无效的刷新令牌')
      }

      const tokens = await this.generateTokens(user)

      return {
        ...tokens,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          avatar: user.avatar,
          roles: user.roles?.map(role => role.code) || []
        }
      }
    } catch (error) {
      throw new UnauthorizedException('无效的刷新令牌')
    }
  }

  async logout(userId: string, refreshToken?: string): Promise<void> {
    // TODO: 实现令牌黑名单机制
    // 这里可以将令牌加入黑名单，防止被继续使用
    console.log(`用户 ${userId} 已登出`)
  }

  async validateUser(payload: JwtPayload): Promise<User> {
    const user = await this.userService.findOne(payload.sub, payload.tenantId)
    
    if (!user || !user.isActive) {
      throw new UnauthorizedException('用户不存在或已被禁用')
    }

    return user
  }

  private async generateTokens(user: User): Promise<{
    accessToken: string
    refreshToken: string
    tokenType: string
    expiresIn: number
  }> {
    const payload: JwtPayload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      tenantId: user.tenantId
    }

    const accessToken = this.jwtService.sign(payload, {
      secret: this.configService.get<string>('JWT_SECRET'),
      expiresIn: this.configService.get<string>('JWT_EXPIRES_IN')
    })

    const refreshToken = this.jwtService.sign(payload, {
      secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
      expiresIn: this.configService.get<string>('JWT_REFRESH_EXPIRES_IN')
    })

    return {
      accessToken,
      refreshToken,
      tokenType: 'Bearer',
      expiresIn: 3600 // 1小时
    }
  }

  private async getTenant(tenantCode?: string): Promise<Tenant> {
    const code = tenantCode || 'default'
    const tenant = await this.tenantService.findByCode(code)
    
    if (!tenant) {
      throw new NotFoundException('租户不存在')
    }

    if (!tenant.isActive) {
      throw new UnauthorizedException('租户已被禁用')
    }

    return tenant
  }

  private async findUserByUsernameOrEmail(usernameOrEmail: string, tenantId: string): Promise<User | null> {
    // 判断是邮箱还是用户名
    const isEmail = usernameOrEmail.includes('@')
    
    if (isEmail) {
      return this.userService.findByEmail(usernameOrEmail, tenantId)
    } else {
      return this.userService.findByUsername(usernameOrEmail, tenantId)
    }
  }
}
