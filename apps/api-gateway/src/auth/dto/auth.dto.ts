import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional } from 'class-validator'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'

export class LoginDto {
  @ApiProperty({ description: '用户名或邮箱', example: 'john_doe' })
  @IsString()
  @MinLength(3)
  @MaxLength(100)
  username: string

  @ApiProperty({ description: '密码', example: 'password123' })
  @IsString()
  @MinLength(6)
  @MaxLength(50)
  password: string

  @ApiPropertyOptional({ description: '租户代码', example: 'default' })
  @IsOptional()
  @IsString()
  tenantCode?: string
}

export class RegisterDto {
  @ApiProperty({ description: '用户名', example: 'john_doe' })
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  username: string

  @ApiProperty({ description: '邮箱', example: '<EMAIL>' })
  @IsEmail()
  @MaxLength(100)
  email: string

  @ApiProperty({ description: '密码', example: 'password123' })
  @IsString()
  @MinLength(6)
  @MaxLength(50)
  password: string

  @ApiPropertyOptional({ description: '租户代码', example: 'default' })
  @IsOptional()
  @IsString()
  tenantCode?: string
}

export class RefreshTokenDto {
  @ApiProperty({ description: '刷新令牌' })
  @IsString()
  refreshToken: string
}

export class ForgotPasswordDto {
  @ApiProperty({ description: '邮箱', example: '<EMAIL>' })
  @IsEmail()
  email: string

  @ApiPropertyOptional({ description: '租户代码', example: 'default' })
  @IsOptional()
  @IsString()
  tenantCode?: string
}

export class ResetPasswordDto {
  @ApiProperty({ description: '重置令牌' })
  @IsString()
  token: string

  @ApiProperty({ description: '新密码', example: 'newpassword123' })
  @IsString()
  @MinLength(6)
  @MaxLength(50)
  newPassword: string
}

export class AuthResponseDto {
  @ApiProperty({ description: '访问令牌' })
  accessToken: string

  @ApiProperty({ description: '刷新令牌' })
  refreshToken: string

  @ApiProperty({ description: '令牌类型', example: 'Bearer' })
  tokenType: string

  @ApiProperty({ description: '过期时间（秒）', example: 3600 })
  expiresIn: number

  @ApiProperty({ description: '用户信息' })
  user: {
    id: string
    username: string
    email: string
    avatar?: string
    roles: string[]
  }
}

export class LogoutDto {
  @ApiPropertyOptional({ description: '刷新令牌' })
  @IsOptional()
  @IsString()
  refreshToken?: string
}
