import { SetMetadata } from '@nestjs/common'

export const PERMISSIONS_KEY = 'permissions'

export interface PermissionRequirement {
  resource: string
  action: string
  conditions?: Record<string, any>
}

export const RequirePermissions = (resource: string, action: string, conditions?: Record<string, any>) =>
  SetMetadata(PERMISSIONS_KEY, { resource, action, conditions } as PermissionRequirement)

export const RequireAnyPermissions = (...permissions: PermissionRequirement[]) =>
  SetMetadata(PERMISSIONS_KEY, { type: 'any', permissions })

export const RequireAllPermissions = (...permissions: PermissionRequirement[]) =>
  SetMetadata(PERMISSIONS_KEY, { type: 'all', permissions })
