import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common'
import { Reflector } from '@nestjs/core'
import { PERMISSIONS_KEY, PermissionRequirement } from '../decorators/permissions.decorator'
import { User } from '../../modules/user/entities/user.entity'

@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const permissionRequirement = this.reflector.getAllAndOverride<PermissionRequirement | any>(
      PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()]
    )

    if (!permissionRequirement) {
      return true
    }

    const request = context.switchToHttp().getRequest()
    const user: User = request.user

    if (!user) {
      throw new ForbiddenException('用户未认证')
    }

    // 处理单个权限要求
    if (permissionRequirement.resource && permissionRequirement.action) {
      return this.checkPermission(user, permissionRequirement)
    }

    // 处理多个权限要求
    if (permissionRequirement.type === 'any') {
      return permissionRequirement.permissions.some((perm: PermissionRequirement) =>
        this.checkPermission(user, perm)
      )
    }

    if (permissionRequirement.type === 'all') {
      return permissionRequirement.permissions.every((perm: PermissionRequirement) =>
        this.checkPermission(user, perm)
      )
    }

    return false
  }

  private checkPermission(user: User, requirement: PermissionRequirement): boolean {
    // 超级管理员拥有所有权限
    if (user.hasRole('super_admin')) {
      return true
    }

    // 检查用户是否有指定权限
    const hasPermission = user.hasPermission(requirement.resource, requirement.action)

    if (!hasPermission) {
      throw new ForbiddenException(
        `权限不足: 需要 ${requirement.resource}:${requirement.action} 权限`
      )
    }

    // TODO: 实现条件权限检查
    if (requirement.conditions) {
      // 这里可以实现更复杂的条件权限逻辑
      // 例如：只能操作自己创建的资源等
    }

    return true
  }
}
