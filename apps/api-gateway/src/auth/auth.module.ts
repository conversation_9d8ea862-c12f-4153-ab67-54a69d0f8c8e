import { Module } from '@nestjs/common'
import { JwtModule } from '@nestjs/jwt'
import { PassportModule } from '@nestjs/passport'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { TypeOrmModule } from '@nestjs/typeorm'

import { AuthService } from './auth.service'
import { AuthController } from './auth.controller'
import { JwtStrategy } from './strategies/jwt.strategy'
import { JwtAuthGuard } from './guards/jwt-auth.guard'
import { PermissionGuard } from './guards/permission.guard'

import { User } from '../modules/user/entities/user.entity'
import { Role } from '../modules/user/entities/role.entity'
import { Tenant } from '../modules/user/entities/tenant.entity'
import { UserService } from '../modules/user/services/user.service'
import { TenantService } from '../modules/user/services/tenant.service'

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Role, Tenant]),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '1h')
        }
      }),
      inject: [ConfigService]
    })
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    UserService,
    TenantService,
    JwtStrategy,
    JwtAuthGuard,
    PermissionGuard
  ],
  exports: [
    AuthService,
    JwtAuthGuard,
    PermissionGuard,
    PassportModule,
    JwtModule
  ]
})
export class AuthModule {}
