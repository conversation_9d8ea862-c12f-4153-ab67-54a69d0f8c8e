{"name": "@link-agent/api-gateway", "version": "1.0.0", "description": "Link Agent API网关服务", "author": "Link Agent Team", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:generate": "typeorm-ts-node-commonjs migration:generate -d src/database/data-source.ts", "migration:run": "typeorm-ts-node-commonjs migration:run -d src/database/data-source.ts", "migration:revert": "typeorm-ts-node-commonjs migration:revert -d src/database/data-source.ts", "seed:run": "ts-node src/database/seeds/run-seeds.ts"}, "dependencies": {"@nestjs/common": "^10.2.8", "@nestjs/core": "^10.2.8", "@nestjs/platform-express": "^10.2.8", "@nestjs/config": "^3.1.1", "@nestjs/typeorm": "^10.0.1", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.2", "@nestjs/swagger": "^7.1.16", "@nestjs/throttler": "^5.0.1", "@nestjs/bull": "^10.0.1", "typeorm": "^0.3.17", "pg": "^8.11.3", "redis": "^4.6.10", "ioredis": "^5.3.2", "bull": "^4.12.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "bcryptjs": "^2.4.3", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "helmet": "^7.1.0", "compression": "^1.7.4", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "multer": "^1.4.5-lts.1", "minio": "^7.1.3", "nodemailer": "^6.9.7", "dayjs": "^1.11.10", "lodash": "^4.17.21", "uuid": "^9.0.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.2.8", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@types/supertest": "^2.0.16", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}