# Link Agent 快速启动指南

## 项目概述

Link Agent 是一个AI业务一体化平台，采用模块化、插件化架构，通过AI技术增强企业业务管理能力。

## 已完成的工作

### ✅ 1. 项目架构设计与技术选型
- 完成整体技术架构设计
- 确定前端技术栈：Vue 3 + TypeScript + Vite + Element Plus
- 确定后端技术栈：NestJS + TypeScript + PostgreSQL + Redis
- 确定AI技术栈：FastAPI + LangChain + PGVector
- 设计插件化架构方案

### ✅ 2. 开发环境搭建
- 创建项目目录结构
- 配置Docker开发环境（PostgreSQL、Redis、MinIO等）
- 搭建前端项目框架（管理后台）
- 搭建后端API网关框架
- 搭建AI服务框架
- 创建开发脚本和工具

### ✅ 3. 平台核心框架开发（进行中）
- 创建共享类型定义包
- 开发插件SDK
- 实现用户管理核心实体
- 开始用户服务开发

## 项目结构

```
link-agent/
├── apps/                          # 应用目录
│   ├── admin-web/                 # 管理后台 (Vue3)
│   ├── api-gateway/               # API网关 (NestJS)
│   └── ai-service/                # AI服务 (FastAPI)
├── packages/                      # 共享包
│   ├── shared-types/              # 共享类型定义
│   └── plugin-sdk/                # 插件开发SDK
├── docker/                        # Docker配置
├── scripts/                       # 开发脚本
└── docs/                          # 文档
```

## 快速开始

### 环境要求
- Node.js 18+
- Python 3.9+
- Docker & Docker Compose
- PostgreSQL 15+
- Redis 7+

### 1. 克隆项目
```bash
git clone <repository-url>
cd link-agent
```

### 2. 环境配置
```bash
# 复制环境变量文件
cp .env.example .env

# 根据需要修改 .env 文件中的配置
```

### 3. 一键启动开发环境
```bash
# 运行开发环境设置脚本
chmod +x scripts/dev-setup.sh
./scripts/dev-setup.sh
```

这个脚本会自动：
- 检查系统要求
- 安装项目依赖
- 启动Docker服务
- 初始化数据库
- 显示服务信息

### 4. 手动启动（可选）
如果自动脚本有问题，可以手动启动：

```bash
# 1. 安装依赖
npm install

# 2. 启动Docker服务
npm run docker:dev

# 3. 启动开发服务
npm run dev
```

## 服务地址

启动成功后，可以访问以下服务：

### 应用服务
- 🌐 管理后台: http://localhost:5173
- 🔌 API网关: http://localhost:3000
- 🤖 AI服务: http://localhost:8000
- 📚 API文档: http://localhost:3000/api/docs

### 基础服务
- 🐘 PostgreSQL: localhost:5432
- 🔴 Redis: localhost:6379
- 📦 MinIO: http://localhost:9001 (admin/admin)
- 🔍 Elasticsearch: http://localhost:9200

### 监控服务
- 📈 Grafana: http://localhost:3000 (admin/admin)
- 🔥 Prometheus: http://localhost:9090
- 📋 Kibana: http://localhost:5601

## 开发命令

```bash
# 启动所有服务
npm run dev

# 分别启动服务
npm run dev:admin    # 管理后台
npm run dev:api      # API网关

# 构建项目
npm run build

# 代码检查
npm run lint
npm run lint:fix

# 格式化代码
npm run format

# 数据库操作
npm run db:migrate   # 运行迁移
npm run db:seed      # 运行种子数据

# Docker操作
npm run docker:dev   # 启动开发环境
npm run docker:down  # 停止所有服务

# 插件开发
npm run plugin:create  # 创建新插件
```

## 下一步计划

### 🔄 当前进行中
- 完成用户管理模块
- 实现权限系统
- 开发多租户支持
- 创建插件管理器

### 📋 待完成任务
1. **平台核心框架开发**
   - 权限系统实现
   - 多租户支持
   - 插件框架完善
   - 审计日志系统

2. **会员体系模块开发**
   - 会员注册登录
   - 付费订阅系统
   - 课程管理
   - 学习进度跟踪

3. **AI服务集成**
   - LLM API集成
   - AI教学助手
   - AI写作助手
   - 向量数据库集成

4. **前端界面开发**
   - 管理后台界面
   - 会员门户界面
   - 响应式设计
   - 用户体验优化

5. **测试与部署**
   - 单元测试
   - 集成测试
   - 性能优化
   - 生产环境部署

## 开发指南

### 插件开发
```bash
# 创建新插件
npm run plugin:create

# 按提示输入插件信息
# 插件会自动创建在 plugins/ 目录下
```

### 代码规范
- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 和 Prettier 配置
- 提交前自动运行代码检查
- 使用 Conventional Commits 规范

### 数据库操作
```bash
# 生成迁移文件
cd apps/api-gateway
npm run migration:generate -- -n CreateUserTable

# 运行迁移
npm run migration:run

# 回滚迁移
npm run migration:revert
```

## 故障排除

### 常见问题

1. **Docker服务启动失败**
   - 检查Docker是否正常运行
   - 确保端口没有被占用
   - 查看Docker日志：`docker-compose logs`

2. **数据库连接失败**
   - 检查PostgreSQL是否启动
   - 验证.env文件中的数据库配置
   - 确保数据库已创建

3. **前端启动失败**
   - 检查Node.js版本（需要18+）
   - 删除node_modules重新安装
   - 检查端口是否被占用

### 获取帮助
- 查看项目文档：`docs/` 目录
- 查看API文档：http://localhost:3000/api/docs
- 提交Issue或联系开发团队

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

欢迎贡献代码和提出建议！
