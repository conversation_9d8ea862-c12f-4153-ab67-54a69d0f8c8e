# 应用配置
NODE_ENV=development
APP_NAME=Link Agent
APP_VERSION=1.0.0
APP_PORT=3000

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=link_agent
DB_SYNC=true
DB_LOGGING=true

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-super-secret-refresh-key
JWT_REFRESH_EXPIRES_IN=30d

# MinIO配置
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET=link-agent
MINIO_USE_SSL=false

# Elasticsearch配置
ELASTICSEARCH_NODE=http://localhost:9200
ELASTICSEARCH_INDEX_PREFIX=link-agent

# AI服务配置
# DeepSeek
DEEPSEEK_API_KEY=your-deepseek-api-key
DEEPSEEK_BASE_URL=https://api.deepseek.com

# 通义千问
QWEN_API_KEY=your-qwen-api-key
QWEN_BASE_URL=https://dashscope.aliyuncs.com/api/v1

# OpenAI
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1

# 本地Ollama
OLLAMA_BASE_URL=http://localhost:11434

# 向量数据库配置
VECTOR_DIMENSION=1536
VECTOR_SIMILARITY_THRESHOLD=0.8

# 邮件配置
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your-email-password
MAIL_FROM=<EMAIL>

# 短信配置
SMS_PROVIDER=aliyun
SMS_ACCESS_KEY=your-sms-access-key
SMS_SECRET_KEY=your-sms-secret-key

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx

# 监控配置
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
JAEGER_PORT=16686

# 日志配置
LOG_LEVEL=debug
LOG_FILE_PATH=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# 安全配置
CORS_ORIGIN=http://localhost:5173,http://localhost:5174
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# 插件配置
PLUGIN_DIR=plugins
PLUGIN_AUTO_LOAD=true
PLUGIN_HOT_RELOAD=true

# 多租户配置
MULTI_TENANT_ENABLED=true
DEFAULT_TENANT=default

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_ITEMS=1000

# 队列配置
QUEUE_REDIS_HOST=localhost
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_DB=1

# 开发工具配置
SWAGGER_ENABLED=true
SWAGGER_PATH=/api/docs
DEBUG_MODE=true
