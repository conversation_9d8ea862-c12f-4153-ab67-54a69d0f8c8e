{"name": "link-agent", "version": "1.0.0", "description": "AI业务一体化平台", "private": true, "workspaces": ["apps/*", "packages/*", "plugins/*/*"], "scripts": {"dev": "concurrently \"npm run dev:admin\" \"npm run dev:member\" \"npm run dev:api\"", "dev:admin": "cd apps/admin-web && npm run dev", "dev:member": "cd apps/member-web && npm run dev", "dev:api": "cd apps/api-gateway && npm run start:dev", "build": "npm run build:admin && npm run build:member && npm run build:api", "build:admin": "cd apps/admin-web && npm run build", "build:member": "cd apps/member-web && npm run build", "build:api": "cd apps/api-gateway && npm run build", "test": "jest", "test:e2e": "cypress run", "lint": "eslint . --ext .ts,.tsx,.vue", "lint:fix": "eslint . --ext .ts,.tsx,.vue --fix", "format": "prettier --write .", "docker:dev": "docker-compose -f docker/docker-compose.dev.yml up -d", "docker:prod": "docker-compose -f docker/docker-compose.prod.yml up -d", "docker:down": "docker-compose down", "db:migrate": "cd apps/api-gateway && npm run migration:run", "db:seed": "cd apps/api-gateway && npm run seed:run", "plugin:create": "node scripts/create-plugin.js", "plugin:install": "node scripts/install-plugin.js", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "concurrently": "^8.2.2", "cypress": "^13.6.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-vue": "^9.18.0", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^15.1.0", "prettier": "^3.1.0", "typescript": "^5.3.0", "vitepress": "^1.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "lint-staged": {"*.{ts,tsx,vue}": ["eslint --fix", "prettier --write"], "*.{js,json,md}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitizen"}}}